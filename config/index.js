import * as path from 'path'

const config = {
  projectName: 'pos_mini',
  date: '2025-1-16',
  designWidth(input) {
    return 375
  },
  deviceRatio: {
    640: 2.34 / 2,
    750: 1,
    828: 1.81 / 2,
    375: 2 / 1,
  },
  alias: {
    '@': path.resolve(__dirname, '../src'),
  },
  sourceRoot: 'src',
  outputRoot: 'dist',
  plugins: [
    '@tarojs/plugin-html',
    [
      '@tarojs/plugin-inject',
      {
        // 配置需要新增的 API
        syncApis: ['getPrivacySetting', 'openPrivacyContract'],
        components: {
          // 配置组件新增 属性和事件
          Button: {
            bindAgreePrivacyAuthorization: '',
          },
        },
      },
    ],
    [
      '@tarojs/plugin-mock',
      {
        host: 'localhost', //	设置数据 mock 服务地址，默认为 127.0.0.1
        port: 9527, //设置数据 mock 服务端口，默认为 9527
      },
    ],


  ],
  env: {
    API_ENV: JSON.stringify(process.env.API_ENV || 'production'),
  },
  defineConstants: {},
  framework: 'react',
  compiler: {
    type: 'webpack5',
    prebundle: { enable: false }, //会报错
  },
  cache: {
    enable: false, // Webpack 持久化缓存配置，建议开启。默认配置请参考：https://docs.taro.zone/docs/config-detail#cache
  },
  sass: {
    projectDirectory: path.resolve(__dirname, '..'),
    resource: [],
  },
  mini: {
    hot: true,
    postcss: {
      pxtransform: {
        enable: true,
        config: {
          rootValue: 37.5,
        },
      },
      url: {
        enable: true,
        config: {
          limit: 1024, // 设定转换尺寸上限
        },
      },
      cssModules: {
        enable: false, // 默认为 false，如需使用 css modules 功能，则设为 true
        config: {
          namingPattern: 'module', // 转换模式，取值为 global/module
          generateScopedName: '[name]__[local]___[hash:base64:5]',
        },
      },
    },
    miniCssExtractPluginOption: {
      ignoreOrder: true,
    },
    optimizeMainPackage: {
      enable: true,
    },
  },
  h5: {
    publicPath: '/',
    staticDirectory: 'static',
    postcss: {
      autoprefixer: {
        enable: true,
        config: {},
      },                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      
      cssModules: {
        enable: false, // 默认为 false，如需使用 css modules 功能，则设为 true
        config: {
          namingPattern: 'module', // 转换模式，取值为 global/module
          generateScopedName: '[name]__[local]___[hash:base64:5]',
        },
      },
    },
  },
}

module.exports = function (merge) {
  if (process.env.NODE_ENV === 'development') {
    return merge({}, config, require('./dev'))
  }
  if (process.env.NODE_ENV === 'uat') {
    return merge({}, config, require('./uat'))
  }
  return merge({}, config, require('./prod'))
}
