import { tabbarListObj } from './constants/config'

export default defineAppConfig({
  pages: [
    'pages/home/<USER>', // 工作台
    'pages/material/index', //物料列表
    'pages/login/index', //登录
		'pages/select-brand/index', //选择品牌
    'pages/materialQuery/index',
    'pages/materialRepay/index',
		'pages/setting/index'
  ],
  subpackages: [
    {
      //审批
      root: 'subpackages/approvaList',
      pages: [
        'index/index',
        'myApplyFor/index', // 申请页面
        'myBack/index', // 归还页面
        'applyForDetail/index', // 订单详情
        'materialApply/index', // 提交审批页面
        'materialApprove/index', // 提交审批页面
        'materialReturn/index', // 物料归还
        'modifyAddress/index', // 修改地址
        'materialAdd/index', // 添加商品页面
        'myApprove/index', // 添加商品页面
      ],
    },
  ],
  window: {
    backgroundColor: '#fff',
    backgroundTextStyle: 'light',
    navigationBarBackgroundColor: '#fff',
    navigationBarTitleText: 'POS',
    navigationBarTextStyle: 'white',
    onReachBottomDistance: 200,
  },
  tabBar: {
    custom: true,
    color: '#ccc',
    selectedColor: '#231815',
    backgroundColor: '#fff',
    borderStyle: 'white',
    list: tabbarListObj,
  },
  requiredPrivateInfos: [],
  permission: {
    'scope.bluetooth': {
      desc: '需要蓝牙权限连接打印机',
    },
    'scope.bluetoothAdjacent': {
      desc: '需要蓝牙设备通信权限',
    },
  },
  usingComponents: Object.assign({
    iconfont: `components/iconfont/${process.env.TARO_ENV}/${process.env.TARO_ENV}`,
  }),
})
