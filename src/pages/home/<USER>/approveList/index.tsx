import { View } from '@tarojs/components'

import { observer } from 'mobx-react'
import { useStores } from '@/hook'
import './index.scss'
import Taro from '@tarojs/taro'
import { useEffect, useState } from 'react'

interface DataType {
  djlx: number | string
  djbh: string
  zdrq: string
  sl: number
  sl_1: number
  zt: string
  zdmc: string
}

interface ApproveListType {
  data: DataType
}

const label = {
  0: '领用',
  1: '借用',
  2: '归还',
}

const ApproveList: React.FC<ApproveListType> = ({ data }) => {
  const { userStore } = useStores()
  const { userInfo } = userStore
  const { approve } = userInfo
  const [labelType, setLabelType] = useState('')

  useEffect(() => {
    if (data.djlx == 2) {
      setLabelType(data.is_xn == 1 ? '异常核销归还' : '实物归还')
    } else {
      setLabelType(label[data.djlx])
    }
  }, [data])

  const getStyle = (value: string) => {

    
    if (value == '待提交') {
      return 'blueStyle'
    } else if (['审批中', '待审批'].includes(value)) {
    console.log('----value',value);


      return 'orangeStyle'
    } else {
      return 'greenStyle'
    }
  }
  const onNavigate = () => {
    if (['待提交'].includes(data.zt)) {
      Taro.navigateTo({
        url: `/subpackages/approvaList/materialApply/index?id=${data.Id}&type=${data.djlx}`,
      })
      return
    }
    if (['待审批'].includes(data.zt)) {
      Taro.navigateTo({
        url: `/subpackages/approvaList/materialApprove/index?id=${data.Id}&type=${data.djlx}&is_xn=${data.is_xn}`,
      })
      return
    } else {
      Taro.navigateTo({
        url: `/subpackages/approvaList/applyForDetail/index?id=${data.Id}&type=${data.djlx}&is_xn=${data.is_xn}`,
      })
    }
  }

  return (
    <View className='ApproveList' onClick={onNavigate}>
      <View className='titleRow'>
        <View className='left'>
          <View className='symbol'>{labelType}</View>
          <View className='title'> {data.djbh}</View>
        </View>
        <View className='right'>{data.rq}</View>
      </View>
      {approve == 1 ? (
        <View className='approveRow'>
          <View className='name'>申请门店/部门：{data.zdmc}</View>
          <View className={['rightButton', getStyle(data.zt)].join(' ')}>{data.zt}</View>
        </View>
      ) : (
        <>
          <View className='approveRow applyFor'>
            <View className='name'>
              {Number(data.sl) > 0 ? '申请数' : '归还数'}：{data.sl || data.sl_1}
            </View>
            <View className={['rightButton', getStyle(data.zt)].join(' ')}>{data.zt}</View>
          </View>
        </>
      )}
    </View>
  )
}

export default observer(ApproveList)
