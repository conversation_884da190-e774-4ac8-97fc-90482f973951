import { View } from '@tarojs/components'
import { observer } from 'mobx-react'
import { useRef, useState } from 'react'
import Taro, { useDidShow } from '@tarojs/taro'
import { useStores } from '@/hook'

import MainLayout from '@/components/MainLayout'
import NeedLogin from '@/components/NeedLogin'
import http from '@/http'
import { getNavBarInfo, onSubscribeMessage } from '@/utils'
import { Image } from '@nutui/nutui-react-taro'
import { IconFont } from '@/components'
import ApproveList from './components/approveList'

import './index.scss'

const HomePage = () => {
  const layoutRef = useRef<any>()
  const { userStore } = useStores()
  const { userInfo } = userStore

  const { navBarAndStatusBarHeight: barHeight, bottomBarHeight } = getNavBarInfo()

  const [list, setList] = useState([])

  // 获取openId
  const getCode = () => {
    Taro.login({
      success: async (res) => {
        const r = await http.common.default.post(
          {
            code: res.code,
            mobile: userInfo.mobile,
          },
          {},
          '?c=user&m=wx_login'
        )
        if (r?.code == 0) {
          userStore.setUserInfo({ openid: r?.data?.openid })
        }
      },
      fail: (res) => {
        Promise.reject(res)
      },
    })
  }

  useDidShow(() => {
    if (userInfo.token) {
      getMyApprove()
    }
  })

  // 获取审批列表
  const getMyApprove = async () => {
    const res = await http.common.default.post(
      { type: 0 },
      {},

      '?c=zdwld&m=list'
    )
    if (res.code == 0) {
      setList(res.data.list)
    }
  }

  const onClickJump = async (path) => {
    const res = await showLoginTip()
    if (!res) return

    onSubscribeMessage(
      () => {
        path == '/pages/material/index'
          ? Taro.switchTab({
              url: '/pages/material/index',
            })
          : Taro.navigateTo({
              url: path,
            })
      },
      () => {}
    )
  }
  const showLoginTip = () => {
    if (!userInfo.token) {
      Taro.showToast({
        title: '请先登录',
        icon: 'none',
        duration: 2000,
      })
      return Promise.resolve(false)
    } else {
      return Promise.resolve(true)
    }
  }

  return (
    <MainLayout
      ref={layoutRef}
      className='home_page'
      initOptions={{
        inited: false,
        initLoading: false,
        loadingFullScreen: true,
      }}
      headBarConfig={{
        showBack: false,
        showSearch: false,
        backgroundColor: 'transparent',
      }}
      showTabBar
      showHeaderBar
      style={{
        background: `url('https://bq-oms-prod.oss-cn-zhangjiakou.aliyuncs.com/mms/images/bg.png?OSSAccessKeyId=LTAI5t6xTosFuZ2TyQuE1EWQ&Expires=1905911750&Signature=2lGQsf2ZOPsYjJy7tItntMm9V8s%3D') no-repeat top center`,
        backgroundSize: '100% auto',
        backgroundColor: '#F7F8FA',
      }}
    >
      <View
        className='content'
        style={{
          height: `calc(100vh - ${barHeight}px - ${bottomBarHeight}px - ${Taro.pxTransform(54)})`,
        }}
      >
        <View className='topArea'>
          {userInfo.token ? (
            <>
              <View className='logo_cont'>
                <View className='imgs'>
                  {userInfo.logoUrl ? (
                    <Image className='logo' mode='heightFix' src={userInfo.logoUrl} />
                  ) : (
                    userInfo.salesBrand
                  )}
                </View>
                <View onClick={() => onClickJump('/pages/setting/index')}>
                  <IconFont name='setting' color={'#F39800'} size={18}></IconFont>
                </View>
              </View>
              <View className='shop_name'>{userInfo.userName}</View>
            </>
          ) : (
            <View className='loginText'> 登录</View>
          )}
        </View>
        {/* <View className='enterArea'>
          <View className='apply' onClick={() => onClickJump('/pages/material/index')}>
            <View className='applyTitle'>
              物料申请
              <View className='iconArea'>
                <IconFont name='lujing' size={8} color='#241804' />
              </View>
            </View>
            <View className='applyTip'>物料借用/领用申请</View>
          </View>
          <View className='right'>
            <View className='repay' onClick={() => onClickJump('/pages/materialRepay/index')}>
              <View className='repayTitle'>
                归还
                <View className='iconArea'>
                  <IconFont name='lujing' size={8} color='#241804' />
                </View>
              </View>
              <View className='repayTip'>物料归还</View>
            </View>
            <View className='bottomArea'>
              <View
                className='myApprove'
                onClick={() => {
                  onClickJump('/subpackages/approvaList/myApprove/index')
                }}
              >
                <View className='myApproveTitle'>
                  我的审批
                  <View className='iconArea'>
                    <IconFont name='lujing' size={8} color='#241804' />
                  </View>
                </View>
              </View>
              <View
                className='myApply'
                onClick={() => {
                  onClickJump('/subpackages/approvaList/myApplyFor/index')
                }}
              >
                <View className='myApplyTitle'>
                  我的申请
                  <View className='iconArea'>
                    <IconFont name='lujing' size={8} color='#241804' />
                  </View>
                </View>
              </View>
            </View>
          </View>
        </View> */}

        <View className='enterArea'>
          <View className='enterAreaTopArea'>
            <View
              className='squareArea applyArea'
              onClick={() => onClickJump('/pages/material/index')}
            >
              <View className='title'>
                物料申请
                <View className='iconArea'>
                  <IconFont name='lujing' size={8} color='#241804' />
                </View>
              </View>
              <View className='tip'>物料借用/领用申请</View>
            </View>
            <View
              className='squareArea backArea'
              onClick={() => onClickJump('/pages/materialRepay/index')}
            >
              <View className='title'>
                归还
                <View className='iconArea'>
                  <IconFont name='lujing' size={8} color='#241804' />
                </View>
              </View>
              <View className='tip'>归还借用的物料</View>
            </View>
          </View>

          <View className='enterAreaBottomArea'>
            <View
              className='squareArea myApplyArea'
              onClick={() => onClickJump('/subpackages/approvaList/myApplyFor/index')}
            >
              <View className='title'>
                我的申请
                <View className='iconArea'>
                  <IconFont name='lujing' size={8} color='#241804' />
                </View>
              </View>
            </View>
            <View
              className='squareArea myBackArea'
              onClick={() => onClickJump('/subpackages/approvaList/myBack/index')}
            >
              <View className='title'>
                我的归还
                <View className='iconArea'>
                  <IconFont name='lujing' size={8} color='#241804' />
                </View>
              </View>
            </View>

            <View
              className='squareArea myApproveArea'
              onClick={() => onClickJump('/subpackages/approvaList/myApprove/index')}
            >
              <View className='title'>
                我的审批
                <View className='iconArea'>
                  <IconFont name='lujing' size={8} color='#241804' />
                </View>
              </View>
            </View>
          </View>
        </View>

        {userInfo.token ? (
          <View
            className='list'
            style={{
              height: `calc(100%  - ${Taro.pxTransform(282)})`,
            }}
          >
            <View className='hasContent'>
              {list?.length ? (
                list?.map((item, index) => {
                  return (
                    <View key={index} className='case'>
                      <ApproveList data={item} />
                    </View>
                  )
                })
              ) : (
                <View className='noData'>
                  <Image
                    src='https://bq-oms-prod.oss-cn-zhangjiakou.aliyuncs.com/mms/images/noLogin.png?OSSAccessKeyId=LTAI5t6xTosFuZ2TyQuE1EWQ&Expires=1905911817&Signature=%2BBtJPQrL5McrqqpARfCOGjw5Myw%3D'
                    className='img'
                  />
                  <View>当前暂无数据</View>
                </View>
              )}
            </View>
          </View>
        ) : (
          <View
            className='list noLogin'
            style={{
              height: `calc(100%  - ${Taro.pxTransform(282)})`,
            }}
          >
            <NeedLogin
              className='oneClickLogin'
              onClick={() => {
                if (!userInfo?.openId) {
                  getCode()
                }
                getMyApprove()
              }}
            >
              一键登录
            </NeedLogin>
          </View>
        )}
        <View></View>
      </View>
    </MainLayout>
  )
}

export default observer(HomePage)
