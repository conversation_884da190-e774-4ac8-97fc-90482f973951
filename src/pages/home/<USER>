.home_page {
  padding: 0 16px;

  .content {
    overflow: hidden;
    box-sizing: border-box;
  }

  .topArea {
    height: 44px;
    margin-top: 20px;
    .logo_cont {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .imgs {
        height: 22px;
        width: 94px;
        font-weight: bolder;
        font-size: 22px;
        line-height: 22px;
        color: #241804;
        // background: url('https://bq-oms-prod.oss-cn-zhangjiakou.aliyuncs.com/mms/images/logo.png?OSSAccessKeyId=LTAI5t6xTosFuZ2TyQuE1EWQ&Expires=1905911762&Signature=LtzmwBzCIKzl515%2FMDVRF9pAPI4%3D') no-repeat center center;
        // background-size: 100% auto;
        .logo {
          width: 100%;
          height: 100%;
        }
      }
    }

    .shop_name {
      font-size: 14px;
      line-height: 20px;
      color: #333544;
      margin-top: 6px;
    }

    .loginText {
      height: 100%;
      line-height: 43px;
      color: #111111;
      font-size: 18px;
      font-weight: 500;
    }
  }

  .enterArea {
    margin-top: 19px;
    .enterAreaTopArea,
    .enterAreaBottomArea {
      display: flex;
      width: 100%;
      .leftArea {
        margin-right: 12px;
      }

      .squareArea {
        flex: 1;
        height: 106px;
        padding: 15px 20px;
        box-sizing: border-box;
        border-radius: 8px;
        .title {
          line-height: 20px;
          font-size: 16px;
          color: #241804;
          font-weight: 500;
          display: flex;
          align-items: center;

          .iconArea {
            margin-left: 4px;
          }
        }

        .tip {
          margin-top: 8px;
          font-size: 12px;
          color: #707070;
          line-height: 16px;
          display: flex;
          align-items: center;

          .iconArea {
            margin-left: 4px;
          }
        }
      }
      .squareArea:not(:last-child) {
        margin-right: 12px;
      }

      .applyArea {
        background: url('https://bq-oms-prod.oss-cn-zhangjiakou.aliyuncs.com/mms/mp/apply.png?OSSAccessKeyId=LTAI5t6xTosFuZ2TyQuE1EWQ&Expires=2067234789&Signature=%2Ftniz3CngL6C6hDDwH9tKliFqNI%3D')
          no-repeat center;
        background-size: 100%;
      }
      .backArea {
        background: url('https://bq-oms-prod.oss-cn-zhangjiakou.aliyuncs.com/mms/mp/back.png?OSSAccessKeyId=LTAI5t6xTosFuZ2TyQuE1EWQ&Expires=2067234802&Signature=C%2Fz4efyRW3uop6HPQdgs9dDEZow%3D')
          no-repeat center;
        background-size: 100%;
      }
      .myApplyArea {
        background: url('https://bq-oms-prod.oss-cn-zhangjiakou.aliyuncs.com/mms/mp/myApply.png?OSSAccessKeyId=LTAI5t6xTosFuZ2TyQuE1EWQ&Expires=2067234812&Signature=bafZchI8chU%2FHOtCHKAw1Ygaku4%3D')
          no-repeat center;
        background-size: 100%;
      }
      .myBackArea {
        background: url('https://bq-oms-prod.oss-cn-zhangjiakou.aliyuncs.com/mms/mp/myBack.png?OSSAccessKeyId=LTAI5t6xTosFuZ2TyQuE1EWQ&Expires=2067234835&Signature=gGQWn1IkSJXQYGJg8oRLLjKpQfY%3D')
          no-repeat center;
        background-size: 100%;
      }

      .myApproveArea {
        background: url('https://bq-oms-prod.oss-cn-zhangjiakou.aliyuncs.com/mms/mp/myApprove.png?OSSAccessKeyId=LTAI5t6xTosFuZ2TyQuE1EWQ&Expires=2067234823&Signature=s2Xn6NjWrGNhtNSpLAHmfy2Ga5I%3D')
          no-repeat center;
        background-size: 100%;
      }
    }
    .enterAreaBottomArea {
      margin-top: 12px;

      .squareArea {
        height: 77px;
        padding: 12px 8px;
        .title {
          font-size: 14px;
          line-height: 20px;
        }
      }
    }
  }

  .list {
    padding-top: 24px;
    box-sizing: border-box;

    .hasContent {
      height: 100%;
      overflow: scroll;

      .case {
        width: 100%;
        margin-bottom: 12px;
        background-color: #fff;
        border-radius: 4px;
      }

      .noData {
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        font-size: 14px;
        color: #707070;

        .img {
          margin-top: 19%;
          width: 213px !important;
          height: 161px !important;
          margin-bottom: 32px;
        }
      }
    }
  }

  .noLogin {
    padding-top: 0;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
