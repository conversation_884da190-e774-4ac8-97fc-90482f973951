import { View } from '@tarojs/components'
import MainLayout from '@/components/MainLayout'
import { getNavBarInfo } from '@/utils'
import { SafeArea } from '@nutui/nutui-react-taro'
import { observer } from 'mobx-react'
import { useState, useEffect } from 'react'

import Taro, { useDidShow } from '@tarojs/taro'
import SearchBar from '@/components/SearchBar'
import SideMenu from '@/components/SideMenu'
import ProductList from '@/components/ProductList'
import CartInfo from '@/components/CartInfo'
import MaterialInfo from '@/components/MaterialInfo'
import { useStores } from '@/hook'
import http from '@/http'
import NoOrder from '@/components/NoOrder'
import cartUtil from '@/utils/cart'
import MaterialApplicationTypePopup from '@/components/MaterialApplicationTypePopup'

import TypeSelect, { RepayType } from '../material/components/TypeSelect'

import './index.scss'

const typeOption: RepayType[] = [
  {
    type: '实物归还',
    tip: '物料完好，需通过物流退回仓库',
    value: 2,
    iconName: 'a-wupinlingyong21',
    iconSize: 16,
  },
  {
    type: '异常核销归还',
    tip: '物料丢失、损毁等无法归还的场景',
    value: 3,
    iconName: 'a-21-xinhaoqiehuan1',
    iconSize: 16,
  },
]

const MaterialRepay = () => {
  const { navBarAndStatusBarHeight: barHeight } = getNavBarInfo()
  const { materialRepayStore, userStore } = useStores()
  const { menus, selectedMenuValue, selectedGoodsList, checkedGoodsList, type, firstOpen } =
    materialRepayStore

  const [materialInfoVisible, setMaterialInfoVisible] = useState(false)
  const [currentGood, setCurrentGood] = useState({})
  const { userInfo } = userStore

  const [typePopupVisible, setTypePopupVisible] = useState(false)
  const [loading, setLoading] = useState(false)

  // useEffect(() => {
  //   if (firstOpen) {
  //     setTypePopupVisible(true)
  //   } else {
  //     setTypePopupVisible(false)
  //   }
  // }, [firstOpen])

  useDidShow(() => {
    setTypePopupVisible(true)
  })

  useEffect(() => {
    materialRepayStore.getMenus(2)
  }, [])

  const onClickSide = (item) => {
    materialRepayStore.setSelectedMenuValue(item.category_code)
  }

  const getGoodInfo = async (item) => {
    const res = await http.common.default.post(
      {
        barcode: item.barcode,
        type: 2,
      },
      {},
      '?c=barcode&m=barcode'
    )
    if (res.code == 0) {
      setCurrentGood(res.data)
      setMaterialInfoVisible(true)
    }
  }

  // 加入购物车
  const onAddCart = (info) => {
    const selectedList = cartUtil.onAddCart(info, selectedGoodsList)
    const checkedList = cartUtil.onAddCart(info, checkedGoodsList)

    const barCodes = checkedList.map((item) => item.barcode)

    selectedList.forEach((item) => {
      const index = barCodes.findIndex((code) => code == item.barcode)
      if (index >= 0) {
        item.sl = checkedList[index].sl
      }
    })

    materialRepayStore.setSelectedGoodsList(selectedList)
    materialRepayStore.setCheckedGoodsList(checkedList)

    setMaterialInfoVisible(false)
  }

  // 更改购物车 商品数量
  const onUpdateGoodSl = (info) => {
    const selectedList = cartUtil.onUpdateGoodSl(info, selectedGoodsList)
    materialRepayStore.setSelectedGoodsList(selectedList)
    const checkedList = cartUtil.onUpdateGoodSl(info, checkedGoodsList)
    materialRepayStore.setCheckedGoodsList(checkedList)
  }

  // 删除购物车商品
  const onDeleteGoods = (value: string[]) => {
    const selectedList = cartUtil.onDeleteGoods(value, selectedGoodsList)
    materialRepayStore.setSelectedGoodsList(selectedList)
    const checkedList = cartUtil.onDeleteGoods(value, checkedGoodsList)
    materialRepayStore.setCheckedGoodsList(checkedList)
  }

  // 提交订单
  const onSubmit = async () => {
    if (!checkedGoodsList.length) {
      Taro.showToast({
        title: '请至少选择一个物料',
        icon: 'none',
        duration: 2000,
      })
      return
    }
    if (loading) return
    setLoading(true)
    const sku = checkedGoodsList.map((item) => {
      return {
        barcode: item.barcode,
        sl: item.sl,
      }
    })
    const orderRes = await http.common.default.post(
      {
        dj_header: JSON.stringify({
          djlx: 2,
          is_xn: type == 2 ? 0 : 1,
        }),
        mx: JSON.stringify(sku),
      },
      {},
      '?c=zdwld&m=add_zdwld'
    )

    if (orderRes.code == 0) {
      Taro.redirectTo({
        url: `/subpackages/approvaList/materialApply/index?id=${orderRes.data.id}&type=${type}`,
      })
      setLoading(false)
      materialRepayStore.setSelectedGoodsList([])
      materialRepayStore.setCheckedGoodsList([])
    } else {
      Taro.showToast({
        title: orderRes.msg,
        icon: 'none',
        duration: 2000,
      })
      setLoading(false)
    }
  }

  const onCheckGoods = (value: string[]) => {
    const res = selectedGoodsList.filter((item) => value.includes(item.barcode))
    materialRepayStore.setCheckedGoodsList(res)
  }

  const onCloseTypePopup = () => {
    setTypePopupVisible(false)
    materialRepayStore.setFirstOpen(false)
  }
  return (
    <MainLayout
      className='materialRepay'
      initOptions={{
        inited: true,
        initLoading: true,
      }}
      headBarConfig={{
        showBack: true,
        showSearch: false,
        backgroundColor: '#fff',
        color: '#000',
        headerTitle: '申请类型' + (type == 2 ? '-实物归还' : '-异常核销归还'),
        icon: '',
      }}
      style={{ backgroundColor: '#F7F8FA' }}
      showHeaderBar
      showTabBar={false}
    >
      {/* 76px 底部tabbar高度 */}
      <View className='content ' style={{ height: `calc(100vh - ${barHeight}px)` }}>
        <View
          className='topArea'
          onClick={(e) => {
            if (!userInfo.token) return
            e.stopPropagation()
            Taro.navigateTo({ url: `/pages/materialQuery/index?type=${type}` })
          }}
        >
          <SearchBar disabled />
        </View>
        {/* <TypeSelect
          styleObject={{
            marginTop: Taro.pxTransform(20),
            padding: `0 ${Taro.pxTransform(16)}`,
          }}
          onSelect={() => {}}
          options={typeOption}
          store={materialRepayStore}
        /> */}
        {menus.length ? (
          <View className='categoryBox'>
            {/* 左侧菜单 */}
            <View className='left'>
              <SideMenu list={menus} onChange={onClickSide} categoryV={selectedMenuValue} />
            </View>
            {/* 右侧商品 */}
            <View className='right'>
              <ProductList list={materialRepayStore.goodsList} onChange={getGoodInfo} />
            </View>
            {!!selectedGoodsList.length && (
              <CartInfo
                popupBottomPosition={0}
                list={selectedGoodsList}
                checklist={checkedGoodsList}
                onUpdateSl={onUpdateGoodSl}
                onDelete={onDeleteGoods}
                onSubmit={onSubmit}
                onCheck={onCheckGoods}
                type={type}
              />
            )}
          </View>
        ) : (
          <View className='noRepay'>
            <NoOrder title='暂无归还的物料' imgType='noReturnMaterial' />
          </View>
        )}

        <MaterialInfo
          visible={materialInfoVisible}
          typeName='归还单'
          onSure={onAddCart}
          onClose={() => setMaterialInfoVisible(false)}
          info={currentGood}
          checklist={checkedGoodsList}
          type={2}
        />

        <MaterialApplicationTypePopup
          options={typeOption}
          visible={typePopupVisible}
          onClose={onCloseTypePopup}
          onSure={onCloseTypePopup}
          store={materialRepayStore}
          type='归还'
        />

        {!!selectedGoodsList.length && <SafeArea position='bottom' />}
      </View>
    </MainLayout>
  )
}

export default observer(MaterialRepay)
