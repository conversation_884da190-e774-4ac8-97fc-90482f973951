import { View, Image } from '@tarojs/components'
import { observer } from 'mobx-react'
import { useRef, useState } from 'react'
import Taro, { useDidShow } from '@tarojs/taro'
import { useStores } from '@/hook'

import { MainLayout, IconFont } from '@/components'
import http from '@/http'
import { getNavBarInfo, onSubscribeMessage, toJump } from '@/utils'
// import { Image } from '@nutui/nutui-react-taro'
import IconNmms from '@/images/icon/icon-nmms.svg'

import './index.scss'

const HomePage = () => {
  const layoutRef = useRef<any>()
  const { userStore } = useStores()
  const { userInfo, storeList } = userStore

  const { navBarAndStatusBarHeight: barHeight, bottomBarHeight } = getNavBarInfo()

  const [list, setList] = useState([])

  useDidShow(() => {})

  const changeBrand = async (item) => {
    const res = await http.common.default.post(
      {
        barcode: userInfo?.mobile,
        type: item.sales_brand,
      },
      {},
      '?c=barcode&m=barcode'
    )
    if (res.code == 0) {
      toJump('pages/home/<USER>')
    }
  }

  return (
    <MainLayout
      ref={layoutRef}
      className='home_select_brand'
      initOptions={{
        inited: false,
        initLoading: false,
        loadingFullScreen: true,
      }}
      headBarConfig={{
        showBack: false,
        showSearch: false,
        backgroundColor: 'transparent',
      }}
      showTabBar={false}
      showHeaderBar
      style={{
        background: `url('https://bq-oms-prod.oss-cn-zhangjiakou.aliyuncs.com/mms/images/bg.png?OSSAccessKeyId=LTAI5t6xTosFuZ2TyQuE1EWQ&Expires=1905911750&Signature=2lGQsf2ZOPsYjJy7tItntMm9V8s%3D') no-repeat top center`,
        backgroundSize: '100% auto',
        backgroundColor: '#F7F8FA',
      }}
    >
      <View
        className='content'
        style={{
          height: `calc(100vh - ${barHeight}px - ${bottomBarHeight}px - ${Taro.pxTransform(54)})`,
        }}
      >
        <View className='log_out' onClick={()=>{userStore.logOut}}>退出登陆</View>
      </View>
    </MainLayout>
  )
}

export default observer(HomePage)
