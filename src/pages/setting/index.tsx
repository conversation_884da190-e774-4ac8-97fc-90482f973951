import { View, Image } from '@tarojs/components'
import { observer } from 'mobx-react'
import { useRef, useState } from 'react'
import Taro, { useDidShow } from '@tarojs/taro'
import { useStores } from '@/hook'
import { Dialog } from '@nutui/nutui-react-taro'

import { MainLayout, IconFont } from '@/components'
import http from '@/http'
import { getNavBarInfo, onSubscribeMessage, toJump } from '@/utils'
// import { Image } from '@nutui/nutui-react-taro'

import './index.scss'

const HomePage = () => {
  const layoutRef = useRef<any>()
  const { userStore } = useStores()
  const { userInfo, storeList } = userStore

  const { navBarAndStatusBarHeight: barHeight, bottomBarHeight } = getNavBarInfo()

  const [list, setList] = useState([])
  const [logoutDialogVisible, setLogoutDialogVisible] = useState(false)

  useDidShow(() => {})

  const changeBrand = async (item) => {
    const res = await http.common.default.post(
      {
        barcode: userInfo?.mobile,
        type: item.sales_brand,
      },
      {},
      '?c=barcode&m=barcode'
    )
    if (res.code == 0) {
      toJump('pages/home/<USER>')
    }
  }

  // 显示退出登录确认对话框
  const handleLogoutClick = () => {
    setLogoutDialogVisible(true)
  }

  // 确认退出登录
  const handleConfirmLogout = () => {
    setLogoutDialogVisible(false)
    userStore.logOut()
  }

  // 取消退出登录
  const handleCancelLogout = () => {
    setLogoutDialogVisible(false)
  }

  return (
    <MainLayout
      ref={layoutRef}
      className='home_select_brand'
      initOptions={{
        inited: false,
        initLoading: false,
        loadingFullScreen: true,
      }}
      headBarConfig={{
        showBack: false,
        showSearch: false,
        backgroundColor: 'transparent',
      }}
      showTabBar={false}
      showHeaderBar
      style={{
        backgroundSize: '100% auto',
        backgroundColor: '#F7F8FA',
      }}
    >
      <View
        className='log_out'
        style={{
          height: `calc(100vh - ${barHeight}px - ${bottomBarHeight}px - ${Taro.pxTransform(54)})`,
        }}
      >
        <View className='btn' onClick={handleLogoutClick}>退出登录</View>
      </View>

      {/* 退出登录确认对话框 */}
      <Dialog
        title="退出登录"
        content="确定要退出登录吗？"
        visible={logoutDialogVisible}
        onConfirm={handleConfirmLogout}
        onCancel={handleCancelLogout}
        confirmText="确定"
        cancelText="取消"
      />

    </MainLayout>
  )
}

export default observer(HomePage)
