.materialQuery {
  text-align: center;


  .content {
    display: flex;
    flex-direction: column;
    background-color: #F7F8FA;

    .topArea {
      padding: 12px 16px;
      // margin-top: 12Px;
      background: #fff;

      .searchBar {
        border-color: #2F2F30;
      }
    }

    .bottomArea {
      flex: 1;
      box-sizing: border-box;
      // margin-top: 12px;
      overflow: scroll;

      .list {
        display: flex;
        justify-content: space-between;
        padding: 16px 24px;
        background-color: #fff;
        border-bottom: 0.5px solid #F0F0F0;
        
        .imgArea {
          width: 96px;
          height: 96px;
          padding: 7px;
          box-sizing: border-box;
          margin-right: 8px;
        }

        .introduce {
          width: calc(100% - 96px - 8px);

          .name {
            line-height: 20px;
            font-size: 14px;
            text-align: left;
          }

          .tip {
            color: #707070;
            font-size: 14px;
            line-height: 14px;
            text-align: left;
            margin-top: 4px;
            height: 32px;
            font-weight: 500;

          }

          .priceRow {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 18px;

            .value {
              font-size: 16px;
              line-height: 20px;
              font-weight: 500;
            }

            .addIcon {
              width: 24px;
              height: 24px;
              background-color: #F39800;
              display: flex;
              justify-content: center;
              align-items: center;
              border-radius: 50%;

            }





          }

        }




      }

      .noData {
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;

      }





    }


  }


}
