import { useState } from 'react'
import Taro, { useRouter } from '@tarojs/taro'
import { View } from '@tarojs/components'
import SearchBar from '@/components/SearchBar'
import MaterialInfo, { GoodType, CurrentGoodType } from '@/components/MaterialInfo'
import MainLayout from '@/components/MainLayout'
import { getNavBarInfo } from '@/utils'
import { Image, SafeArea } from '@nutui/nutui-react-taro'
import IconFont from '@/components/iconfont'
import http from '@/http'
import { useStores } from '@/hook'
import _ from 'lodash'
import NoOrder from '@/components/NoOrder'
import cartUtil from '@/utils/cart'

import { useRequest } from 'ahooks'

import './index.scss'

const MaterialQuery = () => {
  const { navBarAndStatusBarHeight: barHeight } = getNavBarInfo()
  const [visible, setVisible] = useState(false)
  const [route] = useState(useRouter().params)
  const [list, setList] = useState<GoodType[]>([])
  const [currentGood, setCurrentGood] = useState<CurrentGoodType>({})
  const { materialStore, materialRepayStore, loadingStore } = useStores()
  const store = route.type == 2 ? materialRepayStore : materialStore

  const { selectedGoodsList, checkedGoodsList } = store
  const [loading, setLoading] = useState(false)

  const [searchV, setSearchV] = useState('')

  const getList = async (value) => {
    setLoading(true)
    setList([])
    const res = await http.common.default.post(
      {
        djlx: Number(route.type),
        keywords: value,
      },
      {},
      '?c=barcode&m=query'
    )
    setLoading(false)

    if (res.code == 0) {
      setList(res.data.list)
    }
  }

  const { run: onSearch } = useRequest(
    (e) => {
      setSearchV(e)
      if (!e) {
        setList([])
        return
      }
      getList(e)
    },
    {
      debounceWait: 300,
      manual: true,
    }
  )

  const onClickGood = async (item: GoodType) => {
    const res = await http.common.default.post(
      {
        barcode: item.barcode,
        type: route.type == 0 || route.type == 1 ? route.type : 2,
      },
      {},
      '?c=barcode&m=barcode'
    )

    if (res.code == 0) {
      setCurrentGood(res.data)
      setVisible(true)
    }
  }

  const onSure = (info) => {
    const selectedList = cartUtil.onAddCart(info, selectedGoodsList)
    const checkedList = cartUtil.onAddCart(info, checkedGoodsList)

    const barCodes = checkedList.map((item) => item.barcode)

    selectedList.forEach((item) => {
      const index = barCodes.findIndex((code) => code == item.barcode)
      if (index >= 0) {
        item.sl = checkedList[index].sl
      }
    })

    store.setSelectedGoodsList(selectedList)
    store.setCheckedGoodsList(checkedList)
    setVisible(false)
    Taro.navigateBack()
  }

  return (
    <MainLayout
      className='materialQuery'
      initOptions={{
        inited: false,
        initLoading: false,
        loadingFullScreen: true,
      }}
      headBarConfig={{
        showBack: true,
        showSearch: false,
        backgroundColor: '#fff',
        color: '#000',
        headerTitle: '物料查询',
        icon: '',
      }}
      style={{ backgroundColor: '#F7F8FA' }}
      showHeaderBar
      showTabBar={false}
    >
      {/* 76px 底部tabbar高度 */}
      <View className='content ' style={{ height: `calc(100vh - ${barHeight}px)` }}>
        <View className='topArea'>
          <SearchBar onSearch={onSearch} disabled={false} />
        </View>
        <View className='bottomArea'>
          {list.length || !searchV || loading ? (
            list?.map((item, index) => {
              return (
                <View className='list' key={index}>
                  <View className='imgArea'>
                    <Image src={item.img_url} />
                  </View>
                  <View className='introduce'>
                    <View className='name'>{item.goods_name}</View>
                    <View className='tip'>{item?.spms}</View>
                    <View className='priceRow'>
                      <View className='value'>¥{item.goods_price}</View>
                      <View className='addIcon' onClick={() => onClickGood(item)}>
                        <IconFont name='a-Group1561267575' size={12} color='#fff' />
                      </View>
                    </View>
                  </View>
                </View>
              )
            })
          ) : (
            <View className='noData'>
              <NoOrder title='未查询到该物料' imgType='noData' />
            </View>
          )}
          <SafeArea position='bottom' />
        </View>
        <MaterialInfo
          visible={visible}
          typeName='申请单'
          onSure={onSure}
          onClose={() => setVisible(false)}
          info={currentGood}
          type={Number(route.type)}
          checklist={checkedGoodsList}
        />
      </View>
    </MainLayout>
  )
}

export default MaterialQuery
