.home_select_brand {
  padding: 0 16px;

  .content {
    overflow: hidden;
    box-sizing: border-box;
  }

  .topArea {
    margin-top: 20px;
    margin-bottom: 24px;

    .imgs .logo {
      height: 26px;
      width: auto;
    }
    .select-tips {
      font-weight: 500;
      font-style: Medium;
      font-size: 16px;
      line-height: 20px;
      letter-spacing: 0px;
      padding-top: 8px;
      color: #707070;
    }
  }
  .brandList {
    .brandItem {
      padding: 16px;
      margin-bottom: 16px;
      display: flex;
      // justify-content: space-between;
      align-items: center;
      height: calc(88px - 16px - 16px);
      padding: 16px;
      border: 1px solid #ffffff;
      background: #ffffff66;
      backdrop-filter: blur(31.200000762939453px);

      .brandImg {
        width: 56px;
        height: 56px;
        border-radius: 45.5px;
        &.noimg {
          width: 56px;
          height: 56px;
          border-radius: 45.5px;
          background: #e5e5e5;
        }
      }

      .brandDetail {
        padding: 4px 10px;
        .name {
          font-weight: 500;
          font-style: Medium;
          font-size: 16px;
          line-height: 20px;
          margin-bottom: 8px;
        }
        .department {
          font-weight: 400;
          font-size: 14px;
          line-height: 20px;
					color: #707070;
        }
      }
      .checkOp {
        position: absolute;
        right: 16px;
        top: 22px;
      }
    }
  }
}
