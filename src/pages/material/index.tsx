import { View } from '@tarojs/components'
import { observer } from 'mobx-react'
import { useEffect, useState } from 'react'
import { MainLayout } from '@/components'
import Taro, { useDidShow } from '@tarojs/taro'
import { getNavBarInfo } from '@/utils'
import { useStores } from '@/hook'
import SearchBar from '@/components/SearchBar'
import SideMenu from '@/components/SideMenu'
import ProductList from '@/components/ProductList'
import CartInfo from '@/components/CartInfo'
import NeedLogin from '@/components/NeedLogin'
import MaterialInfo from '@/components/MaterialInfo'
import http from '@/http'
import cartUtil from '@/utils/cart'
import MaterialApplicationTypePopup from '@/components/MaterialApplicationTypePopup'

import TypeSelect, { RepayType } from './components/TypeSelect'
import './index.scss'

const typeOption: RepayType[] = [
  {
    type: '领用',
    tip: '领用的物品，无需归还仓库',
    value: 0,
    iconName: 'ling',
    iconSize: 16,
  },
  {
    type: '借用',
    tip: '借用物品，使用后请务必及时归还',
    value: 1,
    iconName: 'jie',
    iconSize: 16,
  },
]


const Material = () => {
  const { navBarAndStatusBarHeight: barHeight,bottomBarHeight } = getNavBarInfo()
  const { materialStore, userStore } = useStores()
  const { menus, selectedMenuValue, selectedGoodsList, checkedGoodsList, type, firstOpen } =
    materialStore
  const { userInfo } = userStore

  const [materialInfoVisible, setMaterialInfoVisible] = useState(false)
  const [currentGood, setCurrentGood] = useState({})
  const [typePopupVisible, setTypePopupVisible] = useState(false)

  useEffect(() => {
    console.log('------userInfo.token', userInfo.token)
   
  }, [firstOpen, userInfo.token])

  useDidShow(() => {
    if (!userInfo.token) return
    setTypePopupVisible(true)
    materialStore.getMenus(type)
  })

  const onClickSide = (item) => {
    materialStore.setSelectedMenuValue(item.category_code)
  }
  const getGoodInfo = async (item) => {
    const res = await http.common.default.post(
      {
        barcode: item.barcode,
        type: type,
      },
      {},
      '?c=barcode&m=barcode'
    )
    if (res.code == 0) {
      setCurrentGood(res.data)
      setMaterialInfoVisible(true)
    }
  }

  // 加入购物车
  const onAddCart = (info) => {
    const selectedList = cartUtil.onAddCart(info, selectedGoodsList)
    const checkedList = cartUtil.onAddCart(info, checkedGoodsList)


    const barCodes = checkedList.map(item =>item.barcode)

    selectedList.forEach(item => {
      const index = barCodes.findIndex(code => code == item.barcode)
      if(index >= 0) {
        item.sl = checkedList[index].sl
      }
    });


    materialStore.setSelectedGoodsList(selectedList)
    materialStore.setCheckedGoodsList(checkedList)
    setMaterialInfoVisible(false)
  }

  // 更改购物车 商品数量
  const onUpdateGoodSl = (info) => {
    const selectedList = cartUtil.onUpdateGoodSl(info, selectedGoodsList)
    materialStore.setSelectedGoodsList(selectedList)
    const checkedList = cartUtil.onUpdateGoodSl(info, checkedGoodsList)
    materialStore.setCheckedGoodsList(checkedList)
  }

  // 删除购物车商品
  const onDeleteGoods = (value: string[]) => {
    const selectedList = cartUtil.onDeleteGoods(value, selectedGoodsList)
    materialStore.setSelectedGoodsList(selectedList)
    const checkedList = cartUtil.onDeleteGoods(value, checkedGoodsList)
    materialStore.setCheckedGoodsList(checkedList)
  }

  // 提交订单
  const onSubmit = async () => {
    if (!checkedGoodsList.length) {
      Taro.showToast({
        title: '请至少选择一个物料',
        icon:'none',
        duration: 2000,

      })
      return
    }

    const sku = checkedGoodsList.map((item) => {
      return {
        barcode: item.barcode,
        sl: item.sl,
      }
    })

    const orderRes = await http.common.default.post(
      {
        dj_header: JSON.stringify({
          djlx: type,
        }),
        mx: JSON.stringify(sku),
      },
      {},
      '?c=zdwld&m=add_zdwld'
    )

    if (orderRes.code == 0) {
      Taro.redirectTo({
        url: `/subpackages/approvaList/materialApply/index?id=${orderRes.data.id}&type=${type}`,
      })
      materialStore.setSelectedGoodsList([])
      materialStore.setCheckedGoodsList([])
    } else {

      Taro.showToast({
        title: orderRes.msg,
        icon:'none',
        duration: 2000

      })
    }
  }

  const onCheckGoods = (value: string[]) => {
    const res = selectedGoodsList.filter((item) => value.includes(item.barcode))
    materialStore.setCheckedGoodsList(res)
  }

  const onCloseTypePopup = () => {
    setTypePopupVisible(false)
    materialStore.setFirstOpen(false)
    materialStore.getMenus(type)
  }

  return (
    <MainLayout
      className='material_page'
      initOptions={{
        inited: true,
        initLoading: true,
        loadingFullScreen: true,
      }}
      headBarConfig={{
        showBack: false,
        showSearch: false,
        backgroundColor: 'transparent',
        color: '#000',
        headerTitle: '申请类型'+ (type==0?'-领用':'-借用'),
        icon: '',
      }}
    >
      {/* 76px 底部tabbar高度 */}
      <View
        className='content '
        style={{ height: `calc(100vh - ${barHeight}px  - ${bottomBarHeight}px - ${Taro.pxTransform(54)} )` }}
      >
        <View
          className='topArea'
          onClick={(e) => {
            if (!userInfo.token) return
            e.stopPropagation()

            Taro.navigateTo({ url: `/pages/materialQuery/index?type=${type}` })
          }}
        >
          <SearchBar disabled />
        </View>

        {userInfo.token ? (
          <>
            {/* <TypeSelect
              styleObject={{
                marginTop: Taro.pxTransform(20),
                padding: `0 ${Taro.pxTransform(16)}`,
              }}
              onSelect={() => {}}
              options={typeOption}
              store={materialStore}
            /> */}
            <View className='categoryBox'>
              {/* 左侧菜单 */}
              <View className='left'>
                <SideMenu list={menus} onChange={onClickSide} categoryV={selectedMenuValue} />
              </View>
              {/* 右侧商品 */}
              <View className='right'>
                <ProductList list={materialStore.goodsList} onChange={getGoodInfo} />
              </View>
              {!!selectedGoodsList.length && (
                <CartInfo
                  type={type}
                  popupBottomPosition={54}
                  list={selectedGoodsList}
                  checklist={checkedGoodsList}
                  onUpdateSl={onUpdateGoodSl}
                  onDelete={onDeleteGoods}
                  onSubmit={onSubmit}
                  onCheck={onCheckGoods}
                />
              )}
            </View>
          </>
        ) : (
          <View className='notLogin'>
            <NeedLogin className='oneClickLogin'>一键登录</NeedLogin>
          </View>
        )}

        <MaterialInfo
          visible={materialInfoVisible}
          typeName='申请单'
          onSure={onAddCart}
          onClose={() => setMaterialInfoVisible(false)}
          info={currentGood}
          type={type}
          checklist={checkedGoodsList}
        />

        <MaterialApplicationTypePopup
          options={typeOption}
          visible={typePopupVisible}
          onClose={onCloseTypePopup}
          onSure={onCloseTypePopup}
          store={materialStore}
          type='申请'
        />
      </View>
    </MainLayout>
  )
}
export default observer(Material)
