.material_page {
  text-align: center;

  .content {
    display: flex;
    flex-direction: column;
    // padding-bottom: calc(env(safe-area-inset-bottom) * var(--nutui-safe-area-multiple, 1));
    box-sizing: border-box;

    .topArea {
      margin: 0 16px;
      margin-top: 12px;
      position: relative;
      box-sizing: border-box;
    }

    .categoryBox {
      flex: 1;
      margin-top: 12px;
      overflow: scroll;
      display: flex;
      position: relative;

      .left {
        width: 100px;
        height: 100%;
      }
      .right {
        flex: 1;
        height: 100%;
      }
    }

    .notLogin {
      flex: 1;

      display: flex;
      align-items: center;
      flex-direction: column;
      justify-content: center;
      .noData {
        width: 202px;
        height: 123px;
        margin-bottom: 34px;
        .img {
          width: 100%;
          height: 100%;
        }
      }
    }
  }
}
