import { View } from '@tarojs/components'
import IconFont from '@/components/iconfont'
import { useStores } from '@/hook'
import { observer } from 'mobx-react'

import './index.scss'

export interface RepayType {
  type: string
  tip: string
  value: number
  iconName: string
  iconSize: number
}

interface TypeSelect {
  store: any
  options: RepayType[]
  onSelect?: () => void
  styleObject?: object
}
const TypeSelect: React.FC<TypeSelect> = ({ store, options, onSelect, styleObject = {} }) => {
  const { type } = store

  const onTypeSelect = (value) => {
    store.setType(value)
    onSelect?.()
  }
  return (
    <View className='typeArea' style={{ ...styleObject }}>
      {options.map((item, index) => {
        return (
          <View
            className={['type', index == 0 ? 'use' : ''].join(' ')}
            key={item.value}
            style={{ borderColor: type == item.value ? '#F39800' : '#999999' }}
            onClick={() => onTypeSelect(item.value)}
          >
            <View
              className='firstRow'
              style={{ color:item.value  == 3?'#E34D59':  type == item.value ? '#F39800' : '#999999' }}
            >
              <View className='symbol'>
                {item.value == 3 ? (
                  <IconFont
                    name={item.iconName as unknown as any}
                    size={item.iconSize}
                    color='#E34D59'
                  />
                ) : (
                  <IconFont
                    name={item.iconName as unknown as any}
                    size={item.iconSize}
                    color={type == item.value ? '#F39800' : '#999999'}
                  />
                )}
              </View>

              <View className='typeText'>{item.type}</View>
            </View>
            <View className='typeTip'>{item.tip}</View>
            <View className='selectBox' style={{ display: type == item.value ? 'auto' : 'none' }}>
              <View className='selectIcon'></View>
              <View className='checkIcon'>
                <IconFont name='check' color='#fff' size={12} />
              </View>
            </View>
          </View>
        )
      })}
    </View>
  )
}

export default observer(TypeSelect)
