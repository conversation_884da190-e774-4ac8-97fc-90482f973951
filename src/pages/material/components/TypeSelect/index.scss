 .typeArea {
      display: flex;
      justify-content: space-between;

      .type {
        flex: 1;
        height: 50px;
        padding: 8px 0;
        border: 1px solid #cccccc;
        box-sizing: border-box;
        border-radius: 4px;
        position: relative;

        .firstRow {
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 12px;
          line-height: 16px;

          .symbol {
            margin-right: 2px;
            display: flex;
            justify-content: center;
            align-items: center;
          }
          .typeText {
            line-height: 16px;
            
          }
        }
        

        .typeTip {
          font-size: 10px;
          line-height: 12px;
          color: #999999;
          margin-top: 6px;
        }

        .selectIcon {
          width: 0;
          height: 0;
          border-left: 26px solid transparent;
          border-bottom: 26px solid transparent;
          border-right: 26px solid #F39800;
          border-radius: 4px 0 0 0;
          position: absolute;
          top: 0;
          right: 0;
        }
        .checkIcon {
          position: absolute;
          top: 1px;
          right:2px;
        }
      }

      .use {
        margin-right: 12px;
      }
    }
