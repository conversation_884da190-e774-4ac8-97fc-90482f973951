.page_login {
  width: 100%;
  height: 100%;

  .page_login_banner {
    height: 400px;
    background-image: url('https://bq-pim.oss-cn-hangzhou.aliyuncs.com/cms/sit/assets/image/f7dbc7ea8a5c4be6d8f1d5ca748772ff.png?x-oss-process=image/format,webp');
    background-size: 100% auto;

    .page_login_title {
      padding-top: 24px;
      width: 100%;
      height: 44px;
      line-height: 44px;
      font-weight: 500;
      color: #ffffff;
      font-size: 20px;
      text-align: center;
    }
  }

  .page_login_form_layout {
    width: calc(100% - 98px);
    background: #ffffff;
    border-radius: 8px;
    margin: -70px auto 0;

    .page_login_form_content {
      width: calc(100% - 62px - 62px);
      margin: 0 auto;
      padding: 2px;

      .page_login_form_title {
        width: 100%;
        height: 140px;
        line-height: 140px;
        text-align: center;
        font-size: 28px;
      }

      .login_form_item {
        width: calc(100% - 4px);
        height: 44px;
        line-height: 46px;
        display: flex;
        flex-direction: row;
        flex-wrap: nowrap;
        border: 2px solid #dddddd;
        border-radius: 6px;
        font-size: 24px;
        margin-bottom: 24px;
        overflow: hidden;
        position: relative;

        &.password {
          margin-bottom: 18px;
          overflow: visible;

          .password_wrong {
            color: #e23834;
            position: absolute;
            top: 36px;
            font-size: 12px;
          }
        }

        &.remember {
          border: none;

          .nut-checkbox-label {
            margin-left: 16px;
            font-size: 18px;
            color: #818181;
          }
        }

        &.first_child {
          margin-top: 20px;
        }

        .form_item_icon {
          width: 54px;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;

          .weapp--icon {
            color: #a7a7a7;
          }
        }

        .icon_line {
          width: 1px;
          height: 20px;
          margin-top: 12px;
          background: #e6e6e6;
        }

        .nut-input {
          padding: 0;
          margin-left: 18px;

          .h5-input {
            font-size: 16px;
            color: #2f2f30;
            height: 100%;
          }

          .input-placeholder {
            color: #818181;
          }
        }

        .form_item_password_type {
          width: 54px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }

      .login_form_submit {
        margin-top: 32px;
        background: #2f2f30;
        height: 60px;
        border-radius: 60px;
        color: #ffffff;
        font-size: 18px;
        width: 100%;
      }
    }
  }
}