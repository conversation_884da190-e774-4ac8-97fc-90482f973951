import Taro from '@tarojs/taro'
import { View } from '@tarojs/components'
import { observer } from 'mobx-react'
import { useStores } from '@/hook'
import { useCallback, useEffect, useState } from 'react'
import { Checkbox, Button, Input } from '@nutui/nutui-react-taro'
import { debounce, upperCase } from 'lodash'
import { IconFont } from '@/components'

import './index.scss'

const LoginPage = () => {
  const { userStore } = useStores()
  // useSlientAuthDidShow(() => {
  //   console.log('useSilentAuthDidShow')
  // })

  const [username, setUsername] = useState<string>('')
  const [password, setPassword] = useState<string>('')
  const [remember, setRemember] = useState<boolean>(false)
  const [passwordInputType, setPasswordInputType] = useState<string>('password')

  const passwordInputTypeChange = () => {
    if (passwordInputType === 'password') {
      setPasswordInputType('text')
    } else {
      setPasswordInputType('password')
    }
  }

  const _loginBtnClick = useCallback(
    debounce(async (param: any) => {
      console.log('param', param)

      await userStore.loginBtnClick(param)
    }, 300),
    []
  )

  const loginBtnClick = () => {
    _loginBtnClick({
      username: username.toUpperCase(),
      password: password,
      remember: remember,
    })
  }

  useEffect(() => {
    try {
      const storageUserStore = Taro.getStorageSync('UserStore')
      if (!storageUserStore || storageUserStore == '') {
        return
      }
      const UserStore = JSON.parse(storageUserStore)
      if (UserStore?.userInfo?.remember) {
        setRemember(true)
        setPasswordInputType('password')
        if (userStore.userInfo.username) {
          setUsername(userStore.userInfo.username)
        }
        if (userStore.userInfo.password) {
          setPassword(userStore.userInfo.password)
        }
      }
    } catch (e) {
      console.error(e)
    }
  }, [])

  const PasswordChange = () => {
    if (password.length == 0) {
      return null
    }
    return (
      <View className='form_item_password_type' onClick={passwordInputTypeChange}>
        {passwordInputType === 'password' ? (
          <IconFont name='a-yanjing1' size={24} color='#a7a7a7' />
        ) : (
          <IconFont name='a-biyanjing1' size={24} color='#a7a7a7' />
        )}
      </View>
    )
  }

  return (
    // <MainLayout
    //   className='page_login'
    //   initOptions={{
    //     inited: true,
    //     initLoading: true,
    //     loadingFullScreen: true,
    //   }}
    //   headBarConfig={{
    //     showBack: false,
    //     showSearch: false,
    //     headerTitle: '登录',
    //     backgroundColor: '#1d242c',
    //     color: '#ffffff'
    //   }}
    //   showTabBar={false}
    // >
    // </MainLayout>
    <View className='page_login'>
      <View className='page_login_banner'>
        <View className='page_login_title'>登录</View>
      </View>
      <View className='page_login_form_layout'>
        <View className='page_login_form_content'>
          <View className='page_login_form_title'>门店收银系统</View>
          <View className='login_form_item first_child'>
            <View className='form_item_icon'>
              <IconFont name='user-profile-01' size={24} color='#a7a7a7' />
            </View>
            <View className='icon_line' />
            <Input
              placeholder='请输入账号'
              type='text'
              value={username}
              onChange={(val) => setUsername(val)}
              cursor={username?.length}
            />
          </View>
          <View className='login_form_item password'>
            <View className='form_item_icon'>
              <IconFont name='lock-02' size={24} color='#a7a7a7' />
            </View>
            <View className='icon_line' />
            <Input
              placeholder='请输入密码'
              type={passwordInputType}
              value={password}
              onChange={(val) => setPassword(val)}
              cursor={password?.length}
            />
            {PasswordChange()}
            {userStore.showPasswordErr ? <View className='password_wrong'>密码错误</View> : null}
          </View>
          <View className='login_form_item remember'>
            <Checkbox
              checked={remember}
              onChange={(val) => {
                setRemember(val)
              }}
              label='记住用户'
              icon={<IconFont name='weixuanzhong' size={22} color='#dddddd' />}
              activeIcon={<IconFont name='Vector' size={22} />}
            />
          </View>
          <Button type='primary' className='login_form_submit' onClick={loginBtnClick}>
            一键登录
          </Button>
        </View>
      </View>
    </View>
  )
}

export default observer(LoginPage)
