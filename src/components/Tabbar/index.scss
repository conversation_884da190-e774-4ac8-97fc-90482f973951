.tab_bar {
  position: fixed;
  bottom: 0px;
  left: 0;
  right: 0;
  background: #fff;
  // height: calc(env(safe-area-inset-bottom) * var(--nutui-safe-area-multiple, 1) + 54px);
  box-shadow: 0px -3px 8px 0px #0000000a;
  z-index: 1000;

  &_content {
    display: flex;
    padding-top: 8px;

    .tab_item {
      flex: 1;
      display: inline-flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      box-sizing: border-box;
      color: #6c6c6c;
      height: 100%;

      .title {
        font-weight: 400;
        font-size: 12px;
        line-height: 16px;
        text-align: center;
        white-space: nowrap;
        color: #bebebe;
      }

      &.is_current .title {
        color: #F39800;
        font-weight: 500;

      }
    }
  }
}
