import { View } from '@tarojs/components'
import Taro, { Current, useDidShow } from '@tarojs/taro'
import { observer } from 'mobx-react'

import { tabbarList, tabbarListObj } from '@/constants/config'
import { useStores } from '@/hook'
import { getNavBarInfo } from '@/utils'
import { useEffect, useState } from 'react'
import FixedView from '../FixedView'
import './index.scss'
import IconFont from '../iconfont'


export interface TabbarProps {}

const TabBar: React.FC<TabbarProps> = (props) => {
  const { loadingStore, cartStore, commonStore } = useStores()
  const [currentIndex, setCurrentIndex] = useState<number>(-1)
  const {bottomBarHeight} = getNavBarInfo()
  const calcCurrentIndex = () => {
    setCurrentIndex(tabbarList.findIndex((i) => i.includes(Current.page?.route)))
  }

  useDidShow(calcCurrentIndex)
  useEffect(calcCurrentIndex, [])

  //切换tabbar
  const handleClick = (i) => {
    if (i === currentIndex) return
    loadingStore.clear()
    Taro.switchTab({ url: `/${tabbarListObj[i].pagePath}` })
  }

  return (
    <FixedView className='tab_bar fixed_bottom_1' position='bottom' highComp>
      <View>
        <View className='tab_bar_content'>
          {tabbarListObj.map((nav, i) => (
            <View
              onClick={() => handleClick(i)}
              key={i}
              className={`tab_item ${i === currentIndex ? 'is_current' : 'item'} `}
            >
              <IconFont
                name={i === currentIndex ? nav.activeIcon : nav.icon}
                size={30}
                color={i === currentIndex ? '#F39800' : '#999999'}
              />
              <View className='title'>{nav.text}</View>
            </View>
          ))}
        </View>
        <View style={{height: bottomBarHeight+'px'}}></View>
      </View>
    </FixedView>
  )
}
TabBar.defaultProps = {}
export default observer(TabBar)
