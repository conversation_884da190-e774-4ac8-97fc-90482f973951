import { View, ScrollView } from '@tarojs/components'
import { MenuType } from '@/mobx/store/material';
import { observer } from 'mobx-react';
import { useRequest } from 'ahooks';

import './index.scss'


export  interface CategoryType {
  name: string;
  value: string
}
interface SideMenuType {
  categoryV:string;
  list: MenuType[]
  onChange: (params: any) => void

}
const SideMenu: React.FC<SideMenuType> = ({list,categoryV,onChange}) => {

 
  const {run:onClickItem} =  useRequest((item) => {
    onChange(item)
  },{
    debounceWait: 300,
    manual: true,
  })
  return (
    <ScrollView className='categoryList' type='list' scrollY scroll-into-view={categoryV}>
      {list?.map((item) => {
        return (
          <View
            id={item.category_code}
            key={item.category_code}
            className={['categoryItem', item.category_code == categoryV ? 'categorySelect' : ''].join(' ')}
            onClick={() => onClickItem(item)}
          >
            {item.category_name}
          </View>
        )
      })}
    </ScrollView>
  )
}

export default observer(SideMenu)
