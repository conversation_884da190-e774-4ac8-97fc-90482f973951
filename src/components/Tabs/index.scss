.pos_tab {


  .nut-tabs-content-wrap {
    display: none;
  }


  .nut-tabs-titles {
    height: 50px;
    background-color: #fff;
    border-bottom: 1px solid #F0F0F0;

    .tabArea {
      width: 116px;
      padding: 0 20px;
      box-sizing: border-box;

      .area {
        height: 100%;
        
        color: #818181;
        font-size: 18px;
        box-sizing: border-box;
        position: relative;

        .title {
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .line {
          position: absolute;
          left: 50%;
          transform: translateX(-50%);
          bottom: 0;
          width: calc(100% - 40px);
          height: 4px;
          background-color: #2F2F30;
          opacity: 0;

        }


      }

    }

    .selectTab {

      .title {

        color: #2F2F30 !important;
        font-size: 20px !important;
        font-weight: 500 !important;
      }

      .line {
        opacity: 1 !important;

      }


    }
  }



}
