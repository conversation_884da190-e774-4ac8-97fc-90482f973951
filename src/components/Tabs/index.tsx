import { View } from "@tarojs/components";
import { Tabs as NutuiTabs } from '@nutui/nutui-react-taro';
import classnames from "classnames";
import './index.scss'
import { useEffect } from "react";


interface tabArrayType {
  title: string;
  value: string | number
}

interface PorpsType {
  tabArray: tabArrayType[]
  v: [string | number, Function]
}
const Tabs = (props: PorpsType) => {
  const { tabArray, v } = props

  return (
    <View>
      <NutuiTabs
        autoHeight
        value={v[0]}
        className='pos_tab'

        title={
          () => {
            {
              return tabArray.map((item) =>
              (
                <View className={classnames('tabArea',{selectTab: item.value == v[0]})}   key={item.title}>
                  <View className='area' onClick={() => v[1](item.value)} >
                    <View className='title'>{item.title}</View>
                    <View className='line'></View>
                  </View>
                </View>
              )
              )
            }
          }
        }
      >
      
      </NutuiTabs>

    </View>
  )
}
export default Tabs

