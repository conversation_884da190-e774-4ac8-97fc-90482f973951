import { Tabs } from '@nutui/nutui-react-taro'
import { View } from '@tarojs/components'
import Taro, { usePageScroll } from '@tarojs/taro'
import { inject, observer } from 'mobx-react'
import { useState, useRef } from 'react'
import './index.scss'

const AnchorPoint = ({ list, curTab, setCurTab, offsetTop = -200 }) => {
  const [isSelectAnchor, setIsSelectAnchor] = useState(false)
  

  usePageScroll((e) => {
    if (isSelectAnchor) return
    const { scrollTop } = e
   

    const query = Taro.createSelectorQuery()
    for (let item of list) {
      query.select(`${item.path}`).boundingClientRect()
    }

    query.select(`#switch-tabs`).boundingClientRect()

    query.exec((rect) => {

      const relativeTop = rect[rect.length - 1]?.top

      const arr = rect.slice(0, rect.length - 1)

      if (scrollTop == 0 || arr[0].bottom > relativeTop + 160  ) {
        setCurTab('#' + arr[0].id)
        return
      }


      const result = arr.find((item) => {
        if (item.top > 0 && item.top - relativeTop < 80) return true
      })


      

      if (result) {
        setCurTab('#'+result.id)
      }
    })
  })

  return (
    <View className='anchorPoint'>
      <Tabs
        value={curTab}
        onChange={(value) => {

          setIsSelectAnchor(true)
          setCurTab(value ?? '')
          const index = list.findIndex((item) => item.path === value)

          if (index == 0) {
            Taro.pageScrollTo({
              scrollTop: 0,
            })
          } else {
            Taro.pageScrollTo({
              selector: value as string | undefined,
              offsetTop: -150,
              duration: 1000,
              success: () => {
                setTimeout(() => {
                  setIsSelectAnchor(false)
                }, 1200)
              },
            })
          }
        }}
      >
        {list.map((item) => {
          return <Tabs.TabPane title={item.label} value={item.path} key={item.path}></Tabs.TabPane>
        })}
      </Tabs>
    </View>
  )
}
export default inject()(observer(AnchorPoint))
