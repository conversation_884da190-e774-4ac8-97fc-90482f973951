.anchorPoint {
  width: 100%;
  box-sizing: border-box;
  ::-webkit-scrollbar {
    display: none;
    width: 0;
    height: 0;
    color: transparent;
  }

  .nut-tabs-titles {
    height: 40px;
    background-color: #fff;
    .nut-tabs-list {
      .nut-tabs-titles-item {
        height: 40px;
        padding: 12px 0 8px;
        box-sizing: border-box;
        flex: 1;
        min-width: auto;
        width: 56px;
        position: relative;

        .nut-tabs-titles-item-text {
          font-size: 14px;
          line-height: 20px;
          color: #999;
        }
      }
      .nut-tabs-titles-item-active {
        position: relative !important;
        .nut-tabs-titles-item-text {
          color: #f39800;
          font-weight: 500;

        }

        .nut-tabs-titles-item-line {
          height: 2px;
          width: 56px;
          background-color: #f39800;
          position: absolute;
          bottom: 0;
          
        }
      }
    }
  }

  .nut-tabs-content-wrap {
    display: none;
  }
}
