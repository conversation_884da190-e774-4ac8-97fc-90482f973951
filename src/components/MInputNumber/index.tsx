import Taro from '@tarojs/taro'
import { View, InputProps } from '@tarojs/components'

import { useEffect, useState } from 'react'
import { Input } from '@tarojs/components'

import IconFont from '../iconfont'
import './index.scss'

interface MInputNumber {
  value: number
  min: number
  max: number
  onInput: (params) => void
  defaultValue?: number
  className?: string
}

const MInputNumber: React.FC<MInputNumber> = ({
  defaultValue,
  value,
  min,
  max,
  onInput,
  className,
}) => {
  const [currentV, setCurrentV] = useState(Number(value))

  useEffect(() => {
    setCurrentV(Number(value))
  }, [value])

  const onMinus = () => {
    if (Number(currentV) <= min) return
    onInput(currentV - 1)
  }
  const onAdd = () => {
    if (Number(currentV) >= max) return
    onInput(currentV + 1)
  }

  const onInputChange = (e) => {
    if (e != '') {
      const num = Number(e)

      if (num > max) {
        console.log('----max', max)

        // setCurrentV(max)
        onInput(max)
        return
      }
      if (num < min) {
        // setCurrentV(min)

        onInput(min)
        return
      }
      onInput(num)
    } else {
      onInput(min)
    }
  }

  const onBlur = (e) => {
    if (e == '') {
      onInput(min)
    }
  }

  return (
    <View className='mInputNumber'>
      <View className='minus' onClick={onMinus}>
        <IconFont
          name='a-Rectangle346240777'
          size={12}
          color={currentV <= min ? '#999999' : '#000'}
        />
      </View>
      <Input
        type='number'
        className={['myInput', className].join(' ')}
        defaultValue={defaultValue?.toString()}
        value={currentV.toString()}
        onInput={(e) => {
          onInputChange(e.detail.value)
        }}
        onBlur={onBlur}
        cursor={currentV.toString().length}
        placeholder=''
        align='center'
      />
      <View className='add' onClick={onAdd}>
        <IconFont name='Vector' size={12} color={currentV >= max ? '#999999' : '#000'} />
      </View>
    </View>
  )
}

export default MInputNumber
