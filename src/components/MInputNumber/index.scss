.mInputNumber {
  display: flex;
  align-items: center;
  border: 1px solid #dedede;
  // width: 61px;
  height: 32px;
  box-sizing: border-box;
  border-radius: 16px;
  .minus {
    width: 40px;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  
  }
  .add {
    width: 40px;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .myInput {
    width: 40px;
    height: 100%;
    font-size: 14px;
    text-align: center;
    padding: 0;
    .nut-input-native {
      // text-align: center !important;



    }

  }
  
}
