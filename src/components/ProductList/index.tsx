import { View, ScrollView, Image } from '@tarojs/components'
import IconFont from '@/components/iconfont'
import { observer } from 'mobx-react'
import _ from 'lodash'
import './index.scss'

export interface goodType {
  barcode: string
  goods_sn: string
  img_url: string
  goods_name: string
  goods_price: string
  quantity: number
}

interface ProductListType {
  list: goodType[]
  onChange: (params: goodType) => void
}

const ProductList: React.FC<ProductListType> = ({ list, onChange }) => {
  return (
    <ScrollView className='goodList' scrollY type='list'>
      <View className='area'>
        {list?.map((item) => {
          return (
            <View key={item.goods_sn} className='good'>
              <View className='goodImg'>
                <Image src={item.img_url} className='imgs' />
                <View
                  className='noStock'
                  style={{
                    display: _.isUndefined(item.quantity) || item.quantity>0 ? 'none' : 'flex',
                  }}
                >
                  暂无库存
                </View>
              </View>
              <View className='goodTitle'>{item.goods_name}</View>
              <View className='goodPrice'>
                <View className='priceInfo'>
                  <View className='priceSign'>¥</View>
                  <View className='priceV'>{item.goods_price}</View>
                </View>
                <View className='addSignArea' onClick={() => onChange(item)}>
                  <View className='addSign'>
                    <IconFont name='a-Group1561267575' size={12} color='#fff' />
                  </View>
                </View>
              </View>
            </View>
          )
        })}
      </View>
    </ScrollView>
  )
}

export default observer(ProductList)
