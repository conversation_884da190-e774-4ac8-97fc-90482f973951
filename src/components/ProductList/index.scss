.goodList {
  height: 100%;
  padding-right: 20px;
  background-color: #fff;
  padding-left: 9px;
  box-sizing: border-box;

  .area {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    padding-bottom: 60px;

    .good {
      width: 117px;
      margin-bottom: 16px;

      .goodImg {
        width: 117px;
        height: 117px;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;

        .imgs {
          width: 100px;
          height: 100px;
        }
        .noStock {
          position: absolute;
          left: 0;
          bottom: 0;
          border-radius: 0  0 4px 4px ;
          background: rgba(0, 0, 0, 0.49);
          display: flex;
          justify-content: center;
          align-items: center;
          color: #fff;
          width: 100%;
          height: 24px;

          font-weight: 400;
          font-size: 12px;
          line-height: 16px;
        }
      }

      .goodTitle {
        margin-top: 4px;
        font-size: 12px;
        line-height: 16px;
        font-weight: 500;
        color: #000;
        text-align: left;
        width: 100%;
        box-sizing: border-box;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .goodPrice {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 8px;
        font-size: 12px;
        line-height: 16px;
        color: #000;

        .priceInfo {
          display: flex;
          align-items: center;

          .priceSign {
          }

          .priceV {
            font-size: 12px;
            line-height: 16px;
            color: #000;
          }
        }

        .addSignArea {
          width: 44px;
          height: 24px;
          display: flex;
          justify-content: center;
          align-items: center;
        }
        .addSign {
          width: 24px;
          height: 24px;
          display: flex;
          justify-content: center;
          align-items: center;
          border-radius: 50%;
          background-color: #f39800;
          position: relative;
        }
      }
    }

    // .good:last-child {
    //   padding-bottom: 100px;
    // }

    // .good:nth-last-child(2) {
    //   padding-bottom: 100px;
    // }
  }
}
