import { View } from '@tarojs/components'
import { Popup, Calendar, Divider, Grid } from '@nutui/nutui-react-taro'
import { getNavBarInfo } from '@/utils'
import { useEffect, useState } from 'react'
import Taro from '@tarojs/taro'

import IconFont from '../iconfont'
import './index.scss'

interface ReceiptType {
  title: string
  value: number
}

const receiptType = [
  {
    title: '领用',
    value: 0,
  },
  {
    title: '借用',
    value: 1,
  },

  {
    title: '实物归还',
    value: 2,
  },
  {
    title: '异常核销归还',
    value: 3,
  },
]
interface ApplyFilterPopupType {
  type: string
  visible: boolean
  setVisible: (flag: boolean) => void
  onChange: (object) => void
}

const ApplyFilterPopup: React.FC<ApplyFilterPopupType> = ({
  type,
  visible,
  setVisible,
  onChange,
}) => {
  const [datePopupShow, setDatePopupShow] = useState(false)

  const [startDate, setStartDate] = useState('')
  const [endDate, setEndDate] = useState('')
  const [dateType, setDateType] = useState('start')
  const [selectReceipt, setSelectReceipt] = useState(-1)
  const [receiptTypeV, setReceiptTypeV] = useState<ReceiptType[]>([])

  const { bottomBarHeight } = getNavBarInfo()

  useEffect(() => {
    if (type == 'apply') {
      const temp = receiptType.filter((item) => item.value < 2)
      setReceiptTypeV(temp)
    } else if (type == 'back') {
      const temp = receiptType.filter((item) => item.value >= 2)
      setReceiptTypeV(temp)
    } else {
      const temp = receiptType.filter((item) => item.value != 2)
      setReceiptTypeV(temp)
    }
  }, [type])

  const openDatePopup = (type) => {
    setDateType(type)
    setDatePopupShow(true)
  }
  const closeDatePopup = () => {
    setDatePopupShow(false)
  }

  const onConfirmDate = (param: string) => {
    const nowDate = param[3].replace(/\//g, '-')

    if (dateType == 'start') {
      if (nowDate > endDate && endDate) {
        Taro.showToast({
          title: '开始时间不能大于结束时间',
          duration: 2000,
          icon: 'none',
        })
        return
      }

      setStartDate(nowDate)
    } else {
      if (nowDate < startDate && startDate) {
        Taro.showToast({
          title: '结束时间不能小于开始时间',
          duration: 2000,
          icon: 'none',
        })
        return
      }
      setEndDate(nowDate)
    }
  }
  const onClickReceipt = (value: number) => {
    if (value == selectReceipt) {
      setSelectReceipt(-1)
    } else {
      setSelectReceipt(value)
    }
  }
  const onSure = () => {
    let info = {
      rq_begin: startDate,
      rq_end: endDate,
    }
    if (selectReceipt != -1) {
      const djlx = selectReceipt == 3 ? 2 : selectReceipt
      const is_xn = selectReceipt == 3 ? 1 : 0
      info = {
        rq_begin: startDate,
        rq_end: endDate,
        djlx: djlx,
        is_xn: is_xn,
      }
    }
    
    onChange(info)
  }

  const onReset = () => {
    setStartDate('')
    setEndDate('')
    setSelectReceipt(-1)
  }

  return (
    <View
      className='applyFilterPopup'
      style={{ '--height': bottomBarHeight + 'px' } as React.CSSProperties}
    >
      <Popup
        visible={visible}
        zIndex={9990}
        position='bottom'
        lockScroll
        closeOnOverlayClick={false}
      >
        <View className='content'>
          <View className='titleRow'>
            <View className='title'>筛选</View>
            <View className='closeIcon' onClick={() => setVisible(false)}>
              <IconFont name='a-Frame427320086' size={20} />
            </View>
          </View>

          <View className='filterArea'>
            <View className='filterItem'>
              <View className='title'>日期</View>
              <View className='dateArea'>
                <View className='dateFrame' onClick={() => openDatePopup('start')}>
                  <View className='date'>
                    {startDate ? <View className='dateV'>{startDate}</View> : '开始时间'}
                  </View>
                  <View className='icon'>
                    <IconFont name='icon' size={16} />
                  </View>
                </View>
                <Divider className='line' />
                <View className='dateFrame' onClick={() => openDatePopup('end')}>
                  <View className='date'>
                    {endDate ? <View className='dateV'>{endDate}</View> : '结束时间'}
                  </View>
                  <View className='icon'>
                    <IconFont name='icon' size={16} />
                  </View>
                </View>
              </View>
            </View>
            <View className='filterItem'>
              <View className='title'>单据类型</View>
              <View className='receiptArea'>
                <View className='row'>
                  {receiptTypeV.map((item, index) => {
                    return (
                      <View
                        key={index}
                        className={[
                          'receiptItem',
                          selectReceipt == item.value ? 'selectReceiptItem' : '',
                        ].join(' ')}
                        onClick={() => onClickReceipt(item.value)}
                      >
                        {item.title}
                        <View
                          className='selectBox'
                          style={{ display: selectReceipt == item.value ? 'flex' : 'none' }}
                        >
                          <View className='selectIcon'></View>
                          <View className='checkIcon'>
                            <IconFont name='check' color='#fff' size={12} />
                          </View>
                        </View>
                      </View>
                    )
                  })}
                </View>
                {/* {type == 'apply' && (
                  <View className='row'>
                    <View
                      className={[
                        'receiptItem lastItem',
                        selectReceipt == receiptType[3].value ? 'selectReceiptItem' : '',
                      ].join(' ')}
                      onClick={() => onClickReceipt(receiptType[3].value)}
                    >
                      {receiptType[3].title}
                      <View
                        className='selectBox'
                        style={{ display: selectReceipt == receiptType[3].value ? 'flex' : 'none' }}
                      >
                        <View className='selectIcon'></View>
                        <View className='checkIcon'>
                          <IconFont name='check' color='#fff' size={12} />
                        </View>
                      </View>
                    </View>
                  </View>
                )} */}
              </View>
            </View>
          </View>

          <View className='footerArea' style={{ paddingBottom: bottomBarHeight + 'px' }}>
            <View className='btn leftBtn' onClick={onReset}>
              重置
            </View>
            <View className='btn rightBtn' onClick={onSure}>
              确定
            </View>
          </View>
        </View>
        <Calendar
          startDate='2025-01-01'
          endDate='2026-01-01'
          visible={datePopupShow}
          defaultValue={dateType == 'start' ? startDate : endDate}
          onClose={closeDatePopup}
          onConfirm={onConfirmDate}
          className='filterCalendar'
          // autoBackfill
          // showToday={false}
        />
      </Popup>
    </View>
  )
}
export default ApplyFilterPopup
