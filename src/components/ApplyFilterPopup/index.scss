.applyFilterPopup {
  .nut-popup {
    padding: 20px 16px 0;
    box-sizing: border-box;

    .content {
      .titleRow {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .title {
          font-weight: 500;
          font-size: 14px;
          line-height: 20px;
          color: #111111;
        }
      }
      .filterArea {
        .filterItem {
          margin-top: 20px;
          .title {
            font-weight: 400;
            font-size: 14px;
            line-height: 20px;
            color: #000000;
            margin-bottom: 16px;
          }
          .dateArea {
            display: flex;
            align-items: center;
            justify-content: space-between;
            .dateFrame {
              width: 132px;
              height: 40px;
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding: 0 12px 0 16px;
              box-sizing: border-box;
              border: 1px solid #f0f0f0;
              border-radius: 4px;
              .date {
                font-weight: 400;
                font-size: 14px;
                line-height: 20px;
                color: #999999;
                .dateV {
                  color: #111111;
                }
              }
            }
            .line {
              flex: 1;
              margin: 0 16px;
            }
          }

          .receiptArea {
            .row {
              display: flex;
              // justify-content: space-between;
              align-items: center;
              .receiptItem {
                position: relative;
                width: 103px;
                height: 40px;
                display: flex;
                justify-content: center;
                align-items: center;
                border: 1px solid #f0f0f0;
                border-radius: 4px;

                font-weight: 400;
                font-size: 14px;
                line-height: 20px;
                color: #999999;
                box-sizing: border-box;

                margin-right: 16px;

                .selectBox {
                  .selectIcon {
                    width: 0;
                    height: 0;
                    border-left: 26px solid transparent;
                    border-bottom: 26px solid transparent;
                    border-right: 26px solid #f39800;
                    border-radius: 4px 0 0 0;
                    position: absolute;
                    top: 0;
                    right: 0;
                  }
                  .checkIcon {
                    position: absolute;
                    top: 1px;
                    right: 2px;
                  }
                }
              }

              .selectReceiptItem {
                border-color: #f39800;
                color: #f39800;
              }

              .lastItem {
                margin-top: 16px;
                width: 116px;
              }
            }
          }
        }
      }

      .footerArea {
        margin-top: 24px;
        padding-top: 9px;
        display: flex;
        justify-content: space-between;
        box-shadow: 0px 0px 8px 0px #0000000a;

        .btn {
          flex: 1;
          font-weight: 400;
          font-size: 14px;
          line-height: 20px;
          border-radius: 4px;
          height: 40px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .leftBtn {
          margin-right: 12px;
          color: #111111;
          border: 1px solid #bebebe;
        }
        .rightBtn {
          color: #fff;
          background-color: #f39800;
        }
      }
    }
  }

  .nut-popup {
    // height: 64% !important;
    // padding: 16px 20px 0;

    .nut-popup-title-right {
      top: 23px !important;
    }

    .filterCalendar {
      // --nutui-calendar-day-width: 46px;
      --nutui-calendar-day-height: 44px;
      --nutui-calendar-active-background-color: #f39800;
      --nutui-calendar-day-active-border-radius: 2px;
      --nutui-calendar-base-font-size: 12px;
      --nutui-calendar-title-font-size: 16px;
      --nutui-calendar-title-font-weight: 400;
      --nutui-calendar-sub-title-font-size: 16px;
      --nutui-calendar-header-height: 24px;

      .nut-calendar-title {
        height: 24px;
        line-height: 24px;
        text-align: left;
      }
      .nut-calendar-week-item:first-of-type,
      .nut-calendar-week-item:last-of-type {
        color: #f39800;
      }

      .nut-calendar-days {
        .nut-calendar-day {
          margin-bottom: 20px;
        }
        .nut-calendar-day {
          .nut-calendar-day-info-curr {
            bottom: 2px;
            font-size: 10px;
          }
        }

        .nut-calendar-day:nth-child(7n),
        .nut-calendar-day:nth-child(7n + 1) {
          color: #f39800;
        }
      }

      .nut-calendar-footer {
        padding-bottom: var(--height);
        .calendar-confirm-btn {
          margin-bottom: 0;
          background: #f39800 !important;
          border-radius: 4px;
          color: #fff;
          font-size: 14px;
        }
      }
    }
  }
}
