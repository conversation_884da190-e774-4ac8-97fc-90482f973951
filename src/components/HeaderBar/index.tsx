import { systemInfo, getNavBarInfo, toJump } from '@/utils'

import { Icon, View } from '@tarojs/components'
import classnames from 'classnames'
import React from 'react'
import { ArrowLeft } from '@nutui/icons-react-taro'
import Taro from '@tarojs/taro'
import { observer } from 'mobx-react'
import { useStores } from '@/hook'
import './index.scss'
import FixedView from '../FixedView'
import IconFont from '../iconfont'


export interface HeaderBarProps {
  headerTitle?: string
  backgroundColor?: string
  color?: string
  showSearch?: boolean
  showBack?: boolean
  icon?: React.ReactNode
}

const HeaderBar: React.FC<HeaderBarProps> = (props) => {
    const { materialStore } = useStores()

  const goBack = () => {
    console.log('-----routes', Taro.getCurrentPages())
    const routes = Taro.getCurrentPages()
    if (routes.length > 1) {
      const recentRoute = routes[routes.length - 1].route
      const prevRoute = routes[routes.length - 2].route

      if (recentRoute == 'subpackages/approvaList/materialApply/index') {
        if (prevRoute !== 'pages/material/index') {
          Taro.navigateBack()
          
        } else {
          materialStore.setSelectedGoodsList([])
          materialStore.setCheckedGoodsList([])

          Taro.switchTab({
            url: '/pages/home/<USER>',
          })
        }
        return 
      }

      if (recentRoute == 'subpackages/approvaList/applyForDetail/index') {
        if (!['subpackages/approvaList/materialApply/index','subpackages/approvaList/materialApprove/index','pages/material/index'].includes(prevRoute) ) {
          Taro.navigateBack()
        } else {
          
          materialStore.setSelectedGoodsList([])
          materialStore.setCheckedGoodsList([])
          Taro.switchTab({
            url: '/pages/home/<USER>',
          })
        }
        return 

      }

      Taro.navigateBack()

      
    } else {
      Taro.reLaunch({
        url: '/pages/home/<USER>',
      })
    }
  }
  const navBarInfo = getNavBarInfo()
  const style = { height: navBarInfo.navBarHeight }

  return (
    <FixedView
      className='header_bar fixed_top_1'
      style={
        {
          '--headerbar-bg-color': props?.backgroundColor || '',
          '--headerbar-color': props?.color || '',
          paddingTop: systemInfo?.statusBarHeight,
        } as any
      }
      highComp
    >
      <View
        className={classnames({
          left_box: true,
          is_multi: props.showBack,
        })}
        style={style}
      >
        {props.showBack ? (
          <View className='left_icon' onClick={goBack}>
            {/* <IconFont name='arrow_back_ios2' size={24} color='#fff' /> */}
            <ArrowLeft size='20px' />
          </View>
        ) : null}
      </View>
      <View className='middle_box' style={style}>
        {props.icon}
        {props.headerTitle && <View className=''>{props.headerTitle || ''}</View>}
      </View>
    </FixedView>
  )
}

HeaderBar.defaultProps = {
  showBack: true,
}

export default observer(HeaderBar)
