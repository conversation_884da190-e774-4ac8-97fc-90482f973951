
.header_bar {
  width: 100%;
  min-height: 44px;
  color: #000;
  background-color: var(--headerbar-bg-color);
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  position: fixed;
  left: 0;
  top: 0;
  padding-top: env(safe-area-inset-top);
  z-index: 1;

  .left_box {
    position: absolute;
    left: 0;
    bottom: 0;
    margin-left: 10px;
    height: 44px;
    font-size: 20px;
    display:flex .iconfont {
      font-size: 20px;
    }

    &.is_multi {
      // margin-bottom: 6px;
      // @include thin-border(all, currentColor, 16px, 0.2);

      .left_icon {
        padding: 6px 12px;
        position: relative;

        &:not(:first-child)::before {
          content: '';
          position: absolute;
          width: 1px;
          height: 18.5px;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          background-color: currentColor;
          opacity: 0.4;
        }
      }
    }
  }

  .middle_box {
    width: 100%;
    height: 44px;
    display: flex;
    font-size: 16px;
    justify-content: center;
    align-items: center;
    font-weight: 500;
  }
}
