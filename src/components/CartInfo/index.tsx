import Taro from '@tarojs/taro'
import { View, ScrollView } from '@tarojs/components'
import { useEffect, useRef, useState } from 'react'
import { Overlay, Checkbox, Image, Dialog } from '@nutui/nutui-react-taro'
import IconFont from '@/components/Iconfont'
import { useRequest } from 'ahooks'
import { getNavBarInfo } from '@/utils'
import { CurrentGoodType } from '../MaterialInfo'
import MInputNumber from '../MInputNumber'

import './index.scss'

const typeV = {
  0: '领用',
  1: '借用',
  2: '实物归还',
  3: '异常核销归还',
}

interface CartInfoType {
  isNeedBottomBar:boolean
  type: number
  popupBottomPosition: number
  list: CurrentGoodType[]
  checklist: CurrentGoodType[]
  onUpdateSl: (v: CurrentGoodType) => void
  onDelete: (v: string[]) => void
  onSubmit: () => void
  onCheck: (v: string[]) => void
  btnText?: string
}

const checkIcon = {
  icon: <IconFont name='weixuanze' size={16} />,
  activeIcon: <IconFont name='quanxuan' size={16} color='#F39800' />,
}
const CartInfo: React.FC<CartInfoType> = ({
  isNeedBottomBar= false,
  type,
  list,
  checklist,
  onCheck,
  onUpdateSl,
  onDelete,
  onSubmit,
  popupBottomPosition = 0,
  btnText = '提交申请',
}) => {
  const [visible, setVisible] = useState(false)
  const checkListRef = useRef(null)
  const [checkbox, setCheckbox] = useState(true)
  const [deleteVisible, setDeleteVisible] = useState(false)
  const [total, setTotal] = useState(0)

  const [selectedV, setSelectedV] = useState([])
  const { bottomBarHeight } = getNavBarInfo()
  const [environment] = useState(Taro.getDeviceInfo())

  useEffect(() => {
    const v = checklist.map((item) => item.barcode)
    setSelectedV(v)
    const res = checklist.reduce((prev, next) => {
      return prev + next.sl
    }, 0)
    setTotal(res)
  }, [checklist])

  const { run: onSubmitFun } = useRequest(
    () => {
      onSubmit()
      setVisible(false)
    },
    {
      throttleWait: 1000,
      manual: true,
    }
  )

  console.log('----environment', environment)

  return (
    <View
      className='cartInfo'
      style={
        {
          '--bottom': Taro.pxTransform(popupBottomPosition),
          '--bottomBarHeight': bottomBarHeight + 'px',
          height: `calc(${Taro.pxTransform(52)} + ${
            environment.system.includes('iOS') || !isNeedBottomBar ? 0 : bottomBarHeight + 'px'
          })`,
        } as React.CSSProperties
      }
    >
      {/* 购物车栏目 */}
      <View>
        <View className='cartRow'>
          <View className='cartRowLeft'>
            <View
              className='cartIcon'
              onClick={() => {
                setVisible(!visible)
              }}
            >
              <IconFont name='contract' size={32} />
            </View>
            <View className='skuInfo'>
              <View className='sku'>
                SKU数
                <View className='skuNum'>{checklist.length}</View>
              </View>
              <View className='briefSummary'>汇总申请数{total}件</View>
            </View>
          </View>
          <View className='cartRowRight' onClick={onSubmitFun}>
            {btnText}
          </View>
        </View>
        {
          isNeedBottomBar &&  <View
            style={{
            height: environment.system.includes('iOS') ? 0 : bottomBarHeight + 'px',
            zIndex: 1200,
            position:'relative',
            background:"#fff",
            
          }}
          ></View>
        }

      </View>

      <Overlay
        closeOnOverlayClick
        visible={visible}
        onClick={(e) => {
          setVisible(false)
        }}
        className='cartOverlay'
      >
        <View className='cartInfoPopupContent'>
          <View className='renderCase' onClick={(e) => e.stopPropagation()}>
            {/* 标题 */}
            <View className='renderTitle'>
              <Checkbox
                {...checkIcon}
                checked={checkbox}
                onChange={(state) => {
                  setCheckbox(state)
                  ;(checkListRef.current as any).toggle(state)
                }}
              ></Checkbox>

              <View className='totalNum'>
                已选
                {total}件
              </View>
              <View className='sign'>{typeV[type]}</View>

              <View
                className='deleteIcon'
                onClick={() => {
                  if (selectedV.length <= 0) {
                    Taro.showToast({
                      title: '请至少选择一个物料',
                      icon: 'none',
                      duration: 2000
                    })
                  } else {
                    setDeleteVisible(true)
                  }
                }}
              >
                <IconFont name='delete' size={20} />
              </View>
            </View>

            {/* 商品列表 */}
            <ScrollView type='list' scrollY className='cartList'>
              <Checkbox.Group
                value={selectedV}
                list
                ref={checkListRef}
                direction='vertical'
                labelPosition='left'
                onChange={(value) => {
                  onCheck(value)
                  if (value.length === list.length) {
                    setCheckbox(true)
                  } else {
                    setCheckbox(false)
                  }
                }}
              >
                {list.map((item) => {
                  return (
                    <Checkbox
                      key={item.barcode}
                      value={item.barcode}
                      {...checkIcon}
                      label={
                        <View
                          className='goodIntroduce'
                          onClick={(e) => {
                            // 防止点击label区域 影响复选框状态
                            e.stopPropagation()
                          }}
                        >
                          <Image src={item.img_url} className='goodImg' />
                          <View className='goodInfo'>
                            <View className='goodTitle'>{item.goods_name}</View>

                            <View className='goodTip' style={{ opacity: item.spms ? 1 : 0 }}>
                              {item.spms}
                            </View>
                            <View className='priceRow'>
                              <View className='goodPrice'>¥{item.goods_price}</View>
                              <View className='goodNum'>
                                {type !== 2 ? (
                                  <>
                                    <MInputNumber
                                      min={1}
                                      max={Number(item.kysl)}
                                      value={item.sl}
                                      onInput={(param) => {
                                        const number = param ?? 1
                                        onUpdateSl({ ...item, sl: number })
                                      }}
                                    />
                                  </>
                                ) : (
                                  <>
                                    <MInputNumber
                                      min={1}
                                      max={Number(item.dgh_sl)}
                                      value={item.sl}
                                      onInput={(param) => {
                                        console.log(param)
                                        const number = param ?? 1
                                        onUpdateSl({ ...item, sl: number })
                                      }}
                                    />
                                  </>
                                )}
                              </View>
                            </View>
                          </View>
                        </View>
                      }
                    />
                  )
                })}
              </Checkbox.Group>
            </ScrollView>
          </View>
        </View>
      </Overlay>

      <Dialog
        title='删除'
        visible={deleteVisible}
        onConfirm={() => {
          onDelete(selectedV)
          setDeleteVisible(false)
        }}
        onCancel={() => setDeleteVisible(false)}
        closeIcon={<IconFont name='a-Frame427320086' size={24} color='#000' />}
        closeIconPosition='top-right'
      >
        确定删除选中商品
      </Dialog>
    </View>
  )
}

export default CartInfo
