.cartInfo {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 52px;

  .cartRow {
    display: flex;
    justify-content: space-between;
    height: 52px;
    background-color: #fff;
    z-index: 1200;
    // position: relative;
    //   box-shadow: 0px 0px 12px 0px #00000014;

    .cartRowLeft {
      flex: 1;
      display: flex;
      align-items: center;
      box-shadow: 0px 0px 12px 0px #00000014;
      padding-bottom: 4px;
      box-sizing: border-box;

      .cartIcon {
        width: 32px;
        height: 32px;
        padding-left: 16px;
        padding-right: 8px;
      }

      .skuInfo {
        .sku {
          font-size: 12px;
          color: #000;
          display: flex;
          align-items: baseline;
          height: 16px;
          line-height: 16px;

          .skuNum {
            font-size: 16px;
            font-weight: 500;
            color: #000;
            margin-left: 4px;
          }
        }

        .briefSummary {
          font-size: 10px;
          color: #707070;
          margin-top: 4px;
        }
      }
    }

    .cartRowRight {
      width: 100px;
      height: 52px;
      font-size: 14px;
      color: #fff;
      background-color: #f39800;
      display: flex;
      align-items: center;
      justify-content: center;
      box-sizing: border-box;
    }
  }

  .cartOverlay {
    // --bottom 是底部tab的高度； 有的有tab 有的不需要
    // 52px 是购物栏列的高度
    // bottom: calc(
    //   env(safe-area-inset-bottom) * var(--nutui-safe-area-multiple, 1) + var(--bottom) + 52px
    // );
    // height: calc(
    //   100vh - env(safe-area-inset-bottom) * var(--nutui-safe-area-multiple, 1) - var(--bottom) -
    //     52px
    // );

    bottom: calc(var(--bottomBarHeight) + var(--bottom) + 52px);
    height: calc(100vh - var(--bottomBarHeight) - var(--bottom) - 52px);
  }

  .cartInfoPopupContent {
    display: flex;
    align-items: flex-end;
    height: inherit;

    .renderCase {
      background-color: #fff;

      width: 100%;
      box-sizing: border-box;
      padding: 0 16px;
      border-bottom: 1px solid #dedede;

      .renderTitle {
        padding: 20px 0 16px;
        display: flex;
        align-items: center;
        position: relative;

        .totalNum {
          font-size: 18px;
          font-weight: 500;
          color: #111111;
          margin-left: 8px;
          display: flex;
          align-items: center;
          width: 75px;
        }

        .sign {
          background-color: #687180;
          border-radius: 4px;
          padding: 2px 6px;
          font-size: 12px;
          color: #fff;
          margin-left: 4px;
        }

        .deleteIcon {
          position: absolute;
          right: 0;
          top: 50%;
          transform: translateY(-50%);
        }
      }

      .cartList {
        max-height: 405px;
        min-height: 162px;
        overflow-y: scroll;

        .nut-checkboxgroup-list {
          padding: 0;
          border-bottom: none;

          .nut-checkbox {
            margin-bottom: 0;
          }

          .nut-checkbox-list-item {
            flex-direction: row-reverse;
            box-sizing: border-box;
            padding: 20px 0 16px 0;

            .nut-checkbox-label {
              padding: 0;
              margin-right: 0;
            }
          }
        }

        .goodIntroduce {
          display: flex;

          .goodImg {
            width: 104px !important;
            height: 104px !important;
            margin: 0 12px;
            background-color: pink;
          }

          .goodInfo {
            flex: 1;
            display: block;
            width: 199px;

            .goodTitle {
              font-size: 14px;
              font-weight: 500;
              color: #000;
              line-height: 20px;
              height: 20px;
              text-align: left;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }

            .goodTip {
              padding: 10px 8px;
              color: #707070;
              font-size: 12px;
              line-height: 16px;
              background-color: #f6f6f6;
              border-radius: 4px;
              margin-top: 4px;
              text-align: left;
            }

            .priceRow {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-top: 16px;

              .goodPrice {
                font-size: 14px;
                font-weight: 500;
              }

              // .goodNum {
              //   border: 1px solid #dedede;
              //   border-radius: 16px;
              //   --nutui-inputnumber-input-background-color: transparent;
              //   --nutui-inputnumber-input-height: 32px;
              //   --nutui-inputnumber-input-font-color: #000;
              //   --nutui-inputnumber-input-font-size: 12px;
              //   --nutui-inputnumber-button-width: 40px;
              //   --nutui-inputnumber-button-height: 32px;
              //   .nut-icon {
              //     font-size: 20px;
              //     width: 20px;
              //     height: 20px;
              //   }
              //   .nut-number-input {
              //     font-size: 14px;
              //   }
              // }
            }
          }
        }
      }
    }
  }

  .nut-dialog-outer {
    border-radius: 4px;
    width: calc(100% - 40px);
    .nut-dialog {
      padding: 24px 16px;
      width: 100%;
      .nut-dialog-header {
        font-size: 16px;
        line-height: 24px;
      }
      .nut-dialog-content {
        font-size: 14px;
        line-height: 20px;
        color: #707070;
        margin: 20px 0 24px;
      }
      .nut-dialog-footer {
        .nut-button {
          width: 140px;
          height: 40px;
          padding: 0;
          box-sizing: border-box;
          border-radius: 4px;
          font-size: 13px;
          &:first-child {
            margin-right: 15px;
          }
        }
        .nut-dialog-footer-cancel {
          border: 1px solid #bebebe;
        }
        .nut-button-primary {
          background: #f39800 !important;
        }
      }
    }
  }
}
