.materialApplicationTypePopup {

  .content {
    .contentTop {
      padding-top: 20px;

      .titleRow {

        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 16px;

        .title {
          font-size: 14px;
          font-weight: 500;
          line-height: 20px;
          color: #111;
        }



      }

      .typeContent {
        padding: 20px 40px 24px 40px;

        .typeArea {
          flex-direction: column;

          // height: 124px;
          .type {
            height: 52px;
            .firstRow {
              font-size: 14px;
              .symbol {
                width: 18px;
                height: 18px;
              }
            }


            .typeTip {
              text-align: center;
              font-size: 12px;
            }
          }

          .type:last-child {
            margin-top: 20px;
          }
          .use {
            margin-right: 0;
          }

        }

      }

    }

    .contentBottom {
      padding: 10px 16px 0;
      box-shadow: 0px 0px 12px 0px #00000014;
      .btn {
        height: 40px;
        width: 100%;
        line-height: 40px;
        text-align: center;
        font-size: 14px;
        color: #fff;
        background: #F39800;
        border-radius: 4px;

      }


    }




  }

}
