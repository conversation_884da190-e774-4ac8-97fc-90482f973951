import { View } from '@tarojs/components'
import { Popup, SafeArea, Button } from '@nutui/nutui-react-taro'
import TypeSelect, { RepayType } from '@/pages/material/components/TypeSelect'
import { getNavBarInfo } from '@/utils'
import IconFont from '../iconfont'
import './index.scss'

interface MaterialApplicationTypePopupType {
  type: string
  options: RepayType[]
  visible: boolean
  onClose: () => void
  onSure: () => void
  store: any
}

const MaterialApplicationTypePopup: React.FC<MaterialApplicationTypePopupType> = ({
  type,
  options,
  visible,
  onClose,
  onSure,
  store,
}) => {
  const {bottomBarHeight} = getNavBarInfo()
  return (
    <View className='materialApplicationTypePopup'>
      <Popup
        visible={visible}
        zIndex={9990}
        position='bottom'
        lockScroll
        closeOnOverlayClick={false}
      >
        <View>
          <View className='content'>
            <View className='contentTop'>
              <View className='titleRow'>
                <View className='title'>选择物料{type}类型</View>
                <View onClick={onClose}>
                  <IconFont name='a-Frame427320086' size={20} />
                </View>
              </View>
              <View className='typeContent'>
                <TypeSelect options={options} store={store} />
              </View>
            </View>
            <View className='contentBottom'>
              <Button type='info' className='btn' onClick={onSure}>
                确认
              </Button>
              <View style={{height:bottomBarHeight+'px'}}></View>
              {/* <SafeArea position='bottom' /> */}
            </View>
          </View>
        </View>
      </Popup>
    </View>
  )
}

export default MaterialApplicationTypePopup
