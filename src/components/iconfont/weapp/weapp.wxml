<!--setting-->
<view wx:if="{{name === 'setting'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M512 36.0448l412.16 237.9776v475.9552L512 987.9552l-412.16-237.9776V274.0224L512 36.0448z m0 98.56L185.088 323.2768v377.3952l326.8096 188.7232 326.8608-188.672V323.2256L511.9488 134.656z m0 249.344a128 128 0 1 0 0 256 128 128 0 0 0 0-256zM298.5984 512a213.3504 213.3504 0 1 1 426.6496 0 213.3504 213.3504 0 0 1-426.6496 0z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--NMMS-->
<view wx:if="{{name === 'NMMS'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 4332 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M3796.48 1002.732308a1455.655385 1455.655385 0 0 1-177.585231-10.712616 1265.624615 1265.624615 0 0 1-166.872615-29.065846l85.740308-252.612923c38.793846 10.24 80.620308 18.904615 125.558153 25.993846a942.473846 942.473846 0 0 0 127.05477 9.216c32.689231 0 61.243077-0.512 85.740307-1.536a1356.406154 1356.406154 0 0 0 70.419693-7.640615l12.248615-76.563692-127.094154-12.209231c-100.036923-10.24-169.393231-32.689231-208.187077-67.387077-38.793846-35.721846-58.171077-84.204308-58.171077-145.447385 0-27.569231 3.072-58.171077 9.176616-91.844923 16.344615-86.764308 39.778462-155.136 70.419692-205.154461 31.625846-50.018462 76.563692-85.188923 134.695385-105.629539C3837.873231 10.712615 3914.909538 0 4010.850462 0c56.123077 0 111.261538 3.072 165.336615 9.176615 54.114462 6.144 106.141538 15.832615 156.16 29.105231l-79.596308 244.972308a1801.373538 1801.373538 0 0 0-113.309538-16.856616 1057.988923 1057.988923 0 0 0-119.414154-6.144c-23.473231 0-43.362462 0.512-59.707077 1.536-16.305231 1.024-36.233846 3.072-59.707077 6.104616l-12.209231 75.027692 102.557539 10.712616c73.491692 7.168 131.150769 21.425231 172.977231 42.889846 41.865846 20.401231 71.443692 46.434462 88.812307 78.060307 17.329231 30.601846 26.033231 66.323692 26.033231 107.126154 0 12.288-1.024 25.048615-3.072 38.321231-1.024 12.209231-2.56 25.481846-4.608 39.778462-14.296615 82.707692-37.257846 147.495385-68.883692 194.441846-31.625846 46.946462-68.883692 81.132308-111.734154 102.557538-42.889846 20.401231-89.836308 33.161846-140.878769 38.281846a1507.643077 1507.643077 0 0 1-153.088 7.640616zM2271.980308 988.711385L2431.684923 31.901538h379.667692l27.569231 486.793847h10.712616L3048.644923 31.901538h379.667692L3378.806154 988.711385h-304.64l0.512-463.872h-10.712616l-127.606153 463.872H2714.387692l-95.940923-463.872h-7.640615l-35.721846 463.872h-303.104zM1028.096 988.711385L1251.603692 31.901538h379.667693l27.56923 486.793847h10.712616L1868.563692 31.901538h379.667693l-113.309539 956.809847h-304.64l64.315077-463.872h-10.712615l-191.409231 463.872h-221.932308l-32.177231-463.872h-7.640615L1331.2 988.711385h-303.104zM39.384615 988.711385L207.793231 31.901538h274.038154l156.120615 339.889231c3.072 7.089231 4.608 17.329231 4.608 30.601846 0 7.089231-0.512 14.769231-1.536 22.961231 0 7.128615-0.512 13.784615-1.536 19.889231h12.248615L725.228308 31.901538H1016.123077l-168.369231 956.809847h-274.038154l-156.16-339.889231a108.583385 108.583385 0 0 0-4.608-13.784616 120.871385 120.871385 0 0 1-1.496615-19.849846v-19.928615c1.024-7.128615 2.008615-13.784615 3.032615-19.889231h-12.20923L330.200615 988.711385H39.384615z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--weixuanze1-->
<view wx:if="{{name === 'weixuanze1'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M512 0C230.4 0 0 230.4 0 512s230.4 512 512 512 512-230.4 512-512S793.6 0 512 0z m0 969.142857C259.657143 969.142857 54.857143 764.342857 54.857143 512S259.657143 54.857143 512 54.857143 969.142857 259.657143 969.142857 512 764.342857 969.142857 512 969.142857z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--checked-->
<view wx:if="{{name === 'checked'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M512 1024a512 512 0 1 0 0-1024 512 512 0 0 0 0 1024zM219.428571 531.017143l59.245715-59.245714 156.16 156.16L770.194286 292.571429l59.245714 59.245714-394.605714 394.605714L219.428571 531.017143z' fill='{{(isStr ? colors : colors[0]) || 'rgb(47,47,48)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--sort-->
<view wx:if="{{name === 'sort'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M128 768v-85.333333h256v85.333333H128z m0-213.333333v-85.333334h512v85.333334H128z m0-213.333334V256h768v85.333333H128z' fill='{{(isStr ? colors : colors[0]) || 'rgb(255,146,47)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--a-Rectangle346240777-->
<view wx:if="{{name === 'a-Rectangle346240777'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 6144 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M0 0h6144v768H0z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--Vector-->
<view wx:if="{{name === 'Vector'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M0 448h1024v128H0v-128z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M576 0v1024h-128V0h128z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--jushou-->
<view wx:if="{{name === 'jushou'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M599.808 248.6784H424.2432v43.8784h175.5648v-43.8784z m-170.0864 192L512 522.9568l82.2784-82.2784a23.2448 23.2448 0 1 1 32.9216 32.9216l-82.2784 82.2784 82.2784 82.2784a23.1936 23.1936 0 1 1-32.9216 32.9216L512 588.8l-82.2784 82.2784a23.296 23.296 0 0 1-32.9216-32.9216l82.2784-82.2784L396.8 473.6a23.1936 23.1936 0 1 1 32.9216-32.9216zM380.416 292.5568H292.608v482.7648h438.8352V292.5568h-87.7568v21.9648c0 10.752-7.7824 19.7632-18.0224 21.6064l-3.9424 0.3072H402.2784a21.9136 21.9136 0 0 1-21.9136-21.9136v-21.9648zM621.7216 204.8c12.1344 0 21.9648 9.8304 21.9648 21.9648v21.9136h109.7216c12.1344 0 21.9136 9.8304 21.9136 21.9648V797.184c0 10.752-7.7312 19.7632-18.0224 21.6064l-3.8912 0.3584H270.6432a21.9136 21.9136 0 0 1-21.9648-21.9648V270.6432c0-12.1344 9.8304-21.9648 21.9648-21.9648h109.7216V226.816c0-12.1344 9.7792-21.9648 21.9136-21.9648h219.4432z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--chushi-->
<view wx:if="{{name === 'chushi'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M805.7856 406.1184l-172.4416 45.4144c-8.8576 2.3552-11.008 11.2128-5.5296 16.0768 7.424 6.656 113.9712 97.792 120.576 103.3216 5.9392 4.9664 16.128 2.4064 18.688-4.9152l51.8144-148.4288c2.7648-8.1408-5.12-13.568-13.1072-11.4688zM695.296 639.5904a25.6 25.6 0 1 0 51.2 0 25.6 25.6 0 0 0-51.2 0z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M741.0176 650.0352v-3.7888l-44.1856-15.104A230.6048 230.6048 0 0 1 486.4 768 230.656 230.656 0 0 1 256 537.6C256 410.5216 359.424 307.2 486.4 307.2a230.7072 230.7072 0 0 1 230.0416 217.344h51.2512a281.6 281.6 0 1 0-23.0912 125.44h-3.584z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--yituihui-->
<view wx:if="{{name === 'yituihui'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M785.8176 569.344c0 83.456-76.8 150.272-170.2912 150.272h-89.856c-13.312 0-26.7264-9.984-26.7264-23.3472s10.0352-23.3984 26.7264-23.3984h89.856c63.4368 0 116.8896-46.7456 116.8896-103.5264 0-56.7808-53.4528-103.5264-116.8896-103.5264h-287.232l80.1792 70.144c3.328 3.328 6.656 6.656 6.656 13.312 0 6.7072-3.328 13.4144-9.984 16.7424-10.0352 6.656-26.7264 6.656-33.4336 0L248.1664 455.8336a18.3808 18.3808 0 0 1-10.0352-16.7424c0-6.656 3.3792-13.312 6.656-16.6912l126.976-110.1824c9.984-6.656 26.6752-6.656 33.3824 0 6.656 6.656 9.984 9.984 9.984 16.6912 0 6.656-3.328 13.312-9.984 16.6912L324.9664 415.744h290.56c93.4912 0 170.2912 70.144 170.2912 153.6z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--yiqianshou-->
<view wx:if="{{name === 'yiqianshou'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M462.848 729.6a32.9728 32.9728 0 0 1-22.272-8.2944l-195.1232-182.9888a29.2864 29.2864 0 0 1-7.168-9.728 27.3408 27.3408 0 0 1-2.4064-11.5712v-0.3584a28.8768 28.8768 0 0 1 9.216-21.3504 33.9968 33.9968 0 0 1 35.1744-6.8096c3.8912 1.536 7.3728 3.6864 10.3424 6.5024l172.032 161.1264 321.8944-301.7216a34.0992 34.0992 0 0 1 22.7328-8.8064 33.9456 33.9456 0 0 1 22.784 9.0624c6.0416 5.632 9.3696 13.312 9.216 21.248v0.4608a26.8288 26.8288 0 0 1-2.2016 11.52 28.8768 28.8768 0 0 1-7.0656 9.728l-344.4224 323.1744a34.0992 34.0992 0 0 1-22.7328 8.8064z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--yunshuzhong1-->
<view wx:if="{{name === 'yunshuzhong1'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M175.8208 581.376l86.9888-130.7648a11.3152 11.3152 0 0 1 9.728-5.1712H358.4V330.24c0-12.6976 10.3424-23.04 23.04-23.04h414.72c12.6464 0 23.04 10.3424 23.04 23.04v322.56c0 12.6976-10.3936 23.04-23.04 23.04h-23.04c0 50.688-41.472 92.16-92.16 92.16s-92.16-41.472-92.16-92.16H404.48c0 50.688-41.472 92.16-92.16 92.16s-92.16-41.472-92.16-92.16h-23.04a23.0912 23.0912 0 0 1-23.04-23.04v-65.0752c0-2.304 0.5632-4.608 1.7408-6.3488zM680.96 721.92c25.344 0 46.08-20.736 46.08-46.08s-20.736-46.08-46.08-46.08-46.08 20.736-46.08 46.08 20.736 46.08 46.08 46.08z m-276.48-92.16h197.0176c16.128-27.648 45.4656-46.08 79.4624-46.08s63.9488 18.432 79.4624 46.08h12.6976V353.28H404.48v276.48z m-92.16 92.16c25.344 0 46.08-20.736 46.08-46.08s-20.736-46.08-46.08-46.08-46.08 20.736-46.08 46.08 20.736 46.08 46.08 46.08z m-92.16-92.16h12.6976c16.0768-27.648 45.4656-46.08 79.4624-46.08 16.6912 0 32.256 4.608 46.08 12.6976V491.52H289.8432c-4.0448 0-7.4752 1.7408-9.216 5.1712l-58.1632 84.1216a12.0832 12.0832 0 0 0-2.304 6.2976v42.6496z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--paisongzhong-->
<view wx:if="{{name === 'paisongzhong'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M329.984 356.4544h46.4384v6.9632a136.9088 136.9088 0 0 0 136.704 136.6016 137.1648 137.1648 0 0 0 136.6016-136.6016 136.9088 136.9088 0 0 0-265.984-45.4656h-53.76c-10.752 0-19.456 8.704-19.456 19.0976 0 10.752 8.704 19.456 19.456 19.456z m280.8832 6.9632a98.304 98.304 0 0 1-98.0992 98.1504 98.304 98.304 0 0 1-98.1504-98.1504c0-2.4576 0-4.5056 0.3072-6.9632h195.584a46.2848 46.2848 0 0 1 0.4096 6.9632zM425.6768 317.952a98.304 98.304 0 0 1 174.08 0h-174.08z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M794.3168 727.9104L672.256 505.6a34.304 34.304 0 0 0-20.1216-18.0736 31.1808 31.1808 0 0 0-28.1088 4.1984c-69.6832 46.4384-152.576 46.4384-222.3104 0a32.256 32.256 0 0 0-26.368-4.1984 35.6352 35.6352 0 0 0-21.504 18.0736l-122.7264 223.3344c-3.84 7.9872-5.2224 16.2816-3.84 24.2688 2.7648 17.664 16.9984 30.5152 33.6384 30.5152h92.928a19.4048 19.4048 0 1 0 0-38.8096H266.1376l119.9616-218.1632a235.008 235.008 0 0 0 254.5664 0l120.0128 218.112h-66.56a19.456 19.456 0 1 0 0 38.912h71.0656c2.048 0 4.1472-0.4096 6.2464-1.0752 13.8752-2.7648 24.6272-14.8992 27.0336-29.8496a40.7552 40.7552 0 0 0-4.096-24.9344z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M621.9776 656.7936h-4.5056v-34.304a33.9968 33.9968 0 0 0-33.28-34.3552H463.4112a32.8192 32.8192 0 0 0-23.2448 9.728 36.4544 36.4544 0 0 0-10.0352 24.576l0.3584 33.9968h-4.864a28.8256 28.8256 0 0 0-21.1456 9.0112 35.328 35.328 0 0 0-10.0864 25.344l0.3584 72.448c0 19.0976 13.824 33.9968 31.232 33.9968h195.9424c17.3056 0 31.232-15.2576 31.232-34.304v-72.4992c0-18.0224-13.9264-33.6384-31.232-33.6384z m-153.2928-29.7984h110.2848v29.7984H468.6848v-29.7984z m145.9712 68.3008v63.488H433.3056l-0.3584-63.488h181.76z' fill='{{(isStr ? colors : colors[2]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--yilanshou-->
<view wx:if="{{name === 'yilanshou'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M795.3408 361.984a14.336 14.336 0 0 0 0-2.048 7.936 7.936 0 0 0 0-1.6896l-2.304-2.7136v-1.1264L752.64 286.5664A62.464 62.464 0 0 0 698.368 256H351.8464a62.464 62.464 0 0 0-54.3744 30.5664l-39.7824 68.9664v0.8704a18.176 18.176 0 0 0-1.536 3.4304 4.096 4.096 0 0 0 0 1.28 18.0736 18.0736 0 0 0 0 4.8128v341.3504A61.1328 61.1328 0 0 0 316.0064 768h418.0992a61.1328 61.1328 0 0 0 61.184-61.1328V361.9328zM333.312 307.2a21.6064 21.6064 0 0 1 18.6368-10.6496h346.3168a21.6576 21.6576 0 0 1 18.6368 10.6496l22.3232 37.8368H310.9376L333.312 307.2z m421.12 399.7696a20.48 20.48 0 0 1-20.3264 20.48H316.0576a20.48 20.48 0 0 1-20.3776-20.48V385.8432h458.8032v321.1264z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M488.5504 599.6032a20.48 20.48 0 1 0-28.4672 28.4672l53.9136 54.5792a40.8576 40.8576 0 0 0 57.7536 0l132.2496-132.096a20.48 20.48 0 0 0 0-28.4672 20.1728 20.1728 0 0 0-28.416 0l-132.7104 132.096-54.3232-54.5792z m-134.144-32.8704h154.2144a20.3264 20.3264 0 0 0 0-40.6528H354.4576a20.3264 20.3264 0 0 0 0 40.6528z m0-94.2592h240.7936a20.48 20.48 0 0 0 0-40.8064H354.4576a20.48 20.48 0 0 0 0 40.8064z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--wentijian-->
<view wx:if="{{name === 'wentijian'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M644.352 256c22.016 0 42.0864 12.2368 52.736 31.0784 0.4096 0.4608 68.1472 117.4016 68.1472 117.4016 1.9456 4.352 3.1744 28.3648 3.1744 33.0752v323.3792A58.112 58.112 0 0 1 710.0416 819.2H489.216a5.12 5.12 0 0 1-1.2288-0.2048 4.8128 4.8128 0 0 0-1.1776-0.2048c-0.768 0.4096-1.536 0.4096-2.3552 0.4096H263.0656A58.1632 58.1632 0 0 1 204.8 760.9344V417.4848c0-4.7616 1.1776-9.472 3.1744-13.3632L274.8928 288.256s0.4096-0.3584 0.4096-0.7168A62.2592 62.2592 0 0 1 329.216 256h315.0848z m-315.4944 40.5504A21.504 21.504 0 0 0 310.272 307.2l-48.0256 83.0976h448.2048l-48.8448-83.8656a20.48 20.48 0 0 0-17.3056-9.8816H328.8576z m159.8976 482.816h221.3376a17.7152 17.7152 0 0 0 17.7152-18.0736V430.08H244.5824v331.264c0 9.8304 8.2432 18.1248 18.1248 18.1248h221.3376c0.4096 0 0.768 0.0512 1.1776 0.2048a4.8128 4.8128 0 0 0 1.1776 0.1536c0.768-0.4096 1.536-0.4096 2.3552-0.4096z m0-320.512c-68.9152 0-87.04 55.5008-87.04 84.6336 0 13.0048 10.6496 23.6544 23.6544 23.6544a23.7056 23.7056 0 0 0 23.6032-23.6544c0-6.2976 1.9456-37.376 40.192-37.376 31.488 0 35.0208 24.0128 35.0208 34.2528 0 13.3632-6.656 19.712-23.6032 31.8976-15.36 11.008-35.84 26.368-35.0208 54.784v24.7808a23.7056 23.7056 0 0 0 47.2576 0v-25.6c0-3.1232 3.4816-6.656 16.128-16.128l0.2048-0.1536c16.896-12.5952 42.3424-31.488 42.3424-69.9392-0.4096-39.0144-26.0096-81.152-82.7392-81.152zM486.4 695.5008a27.2384 27.2384 0 0 0 0 54.3232c14.9504 0 27.136-12.2368 27.136-27.136a27.7504 27.7504 0 0 0-27.136-27.1872z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--ling-->
<view wx:if="{{name === 'ling'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M526.620444 386.730667h67.811556c4.892444-14.677333 10.808889-31.459556 15.189333-46.648889H514.161778v-56.433778h249.514666v56.433778h-94.947555c-6.485333 16.270222-13.027556 31.971556-18.944 46.648889h101.944889v251.619555h-60.188445V432.810667h-107.975111v207.701333h-56.888889V386.730667z m-2.673777 27.648l-39.082667 44.487111c-17.863111-27.704889-53.134222-68.380444-86.755556-102.513778-27.648 46.08-60.757333 88.405333-99.271111 121.457778a316.586667 316.586667 0 0 0-42.837333-42.268445c55.296-43.406222 103.025778-115.541333 128-179.541333l55.296 16.270222c-4.323556 11.377778-9.216 22.755556-14.620444 34.133334 35.84 35.271111 78.620444 79.246222 99.271111 107.975111z m-173.568 11.946666l40.675555-24.974222c20.081778 19.000889 44.487111 44.487111 56.376889 60.188445l-41.187555 29.866666a555.064889 555.064889 0 0 0-55.864889-65.080889z m111.729777 66.161778l40.675556 25.486222c-20.593778 52.622222-56.433778 112.810667-86.243556 157.809778 16.839111 16.839111 30.890667 33.109333 40.675556 46.648889l-46.08 38.001778c-21.731556-32.597333-71.111111-83.569778-109.056-121.002667l42.894222-31.971555c9.728 9.216 20.593778 19.000889 31.971556 28.728888 16.270222-25.486222 34.133333-56.888889 48.298666-85.674666H300.487111v-54.784h151.324445l10.24-3.242667z m180.053334 170.837333l28.216889-24.462222c33.109333 26.112 77.539556 62.976 98.702222 87.381334l-41.244445 38.513777c-18.944-23.893333-59.107556-60.245333-91.079111-87.324444-20.593778 42.325333-55.864889 70.542222-118.784 90.567111a155.306667 155.306667 0 0 0-30.947555-44.999111c121.514667-36.408889 124.245333-97.621333 124.757333-271.189333h52.622222c-0.568889 94.378667-2.730667 162.133333-22.186666 211.512888z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M796.444444 960V1024H227.555556v-64h568.888888zM960 796.444444V227.555556A163.555556 163.555556 0 0 0 796.444444 64H227.555556A163.555556 163.555556 0 0 0 64 227.555556v568.888888A163.555556 163.555556 0 0 0 227.555556 960V1024l-11.719112-0.284444a227.555556 227.555556 0 0 1-215.608888-215.552L0 796.444444V227.555556A227.555556 227.555556 0 0 1 215.836444 0.284444L227.555556 0h568.888888l11.719112 0.284444A227.555556 227.555556 0 0 1 1024 227.555556v568.888888l-0.284444 11.719112a227.555556 227.555556 0 0 1-215.552 215.608888L796.444444 1024v-64A163.555556 163.555556 0 0 0 960 796.444444z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--jie-->
<view wx:if="{{name === 'jie'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M716.231111 426.894222h-38.229333v-53.475555h27.306666c25.144889 0 38.798222 0.568889 51.882667 2.104889v-56.263112c-15.36 2.161778-30.606222 2.730667-52.451555 2.730667h-26.737778v-13.653333c0-19.114667 1.137778-32.142222 2.730666-44.714667h-61.667555c1.649778 11.946667 2.730667 25.144889 2.730667 44.259556v14.222222H543.175111v-13.084445c0-20.764444 0.568889-33.848889 2.730667-45.340444H483.726222c2.161778 13.084444 3.299556 25.6 3.299556 44.771556v13.653333h-18.033778c-22.414222 0-37.603556-0.568889-50.801778-2.730667v56.263111c12.629333-1.706667 26.225778-2.161778 50.232889-2.161777h18.545778v53.475555H456.248889c-28.330667 0-43.064889-0.568889-55.182222-2.673778v57.799111c15.815111-2.161778 31.118222-2.730667 56.32-2.730666h257.592889c26.225778 0 42.666667 1.080889 56.32 3.299555V424.277333c-12.515556 1.536-28.330667 2.616889-55.125334 2.616889z m-94.378667 0h-78.506666v-53.475555h78.506666v53.475555z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M378.311111 396.856889v307.882667c0 24.576 0.568889 42.609778 2.787556 63.260444H317.724444c2.104889-20.195556 3.299556-37.660444 3.299556-63.829333v-162.133334c0-13.084444 0-13.084444 1.706667-61.724444-23.552 38.229333-26.737778 43.121778-41.528889 61.155555-7.68-26.737778-13.084444-42.097778-25.201778-64.967111 36.067556-38.798222 65.536-88.405333 87.324444-148.48 11.548444-30.606222 15.815111-46.990222 19.114667-72.021333l62.805333 14.734222c-1.080889 3.299556-6.542222 19.683556-15.815111 48.071111-8.647111 26.168889-17.408 48.014222-31.061333 78.051556z m350.492445 371.143111a717.937778 717.937778 0 0 1-2.730667-64.910222V558.990222c0-22.414222 0.568889-35.498667 2.161778-47.502222-11.946667 1.706667-24.576 2.161778-50.801778 2.161778H486.343111c-23.950222 0-36.067556-0.568889-49.607111-2.161778 1.706667 13.084444 2.161778 25.201778 2.161778 45.340444v149.447112c0 24.007111-1.137778 43.121778-3.299556 61.724444h60.017778v-25.144889h172.430222v25.144889h60.757334zM495.729778 562.801778h172.430222v40.96H495.729778v-40.96z m0 130.446222v-42.040889h172.430222v42.040889H495.729778z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M796.444444 960V1024H227.555556v-64h568.888888zM960 796.444444V227.555556A163.555556 163.555556 0 0 0 796.444444 64H227.555556A163.555556 163.555556 0 0 0 64 227.555556v568.888888A163.555556 163.555556 0 0 0 227.555556 960V1024l-11.719112-0.284444a227.555556 227.555556 0 0 1-215.608888-215.552L0 796.444444V227.555556A227.555556 227.555556 0 0 1 215.836444 0.284444L227.555556 0h568.888888l11.719112 0.284444A227.555556 227.555556 0 0 1 1024 227.555556v568.888888l-0.284444 11.719112a227.555556 227.555556 0 0 1-215.552 215.608888L796.444444 1024v-64A163.555556 163.555556 0 0 0 960 796.444444z' fill='{{(isStr ? colors : colors[2]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--quanxuan-->
<view wx:if="{{name === 'quanxuan'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M896 0a128 128 0 0 1 128 128v768a128 128 0 0 1-128 128H128a128 128 0 0 1-128-128V128a128 128 0 0 1 128-128h768zM420.224 623.36L264.128 467.2l-59.328 59.328 215.424 215.488 394.624-394.688-59.264-59.328-335.36 335.36z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--weixuanze-->
<view wx:if="{{name === 'weixuanze'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M921.6 972.8v51.2H102.4v-51.2h819.2z m51.2-51.2V102.4a51.2 51.2 0 0 0-46.016-50.944L921.664 51.2H102.4a51.2 51.2 0 0 0-51.2 51.2v819.2a51.2 51.2 0 0 0 51.2 51.2v51.2l-10.432-0.512a102.464 102.464 0 0 1-91.52-91.456L0 921.6V102.4A102.4 102.4 0 0 1 91.968 0.512L102.4 0h819.2l10.432 0.512A102.4 102.4 0 0 1 1024 102.4v819.2l-0.512 10.432a102.464 102.464 0 0 1-91.456 91.52L921.6 1024v-51.2a51.2 51.2 0 0 0 51.2-51.2z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--yitongyi-->
<view wx:if="{{name === 'yitongyi'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1331 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M1164.288 23.04a102.4 102.4 0 0 1 143.872 143.9744l-6.9632 7.7824L512 963.9936 30.0032 481.9968l-6.9632-7.7824A102.4 102.4 0 0 1 166.912 330.24l7.7824 6.9632L512 674.4064 1156.4032 30.0032l7.7824-6.9632z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--sousuo-->
<view wx:if="{{name === 'sousuo'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M984.576 922.7776l-167.0144-167.0144c139.6736-170.2912 129.9456-422.6048-29.696-582.144C618.496 4.096 343.552 4.096 173.568 173.568c-169.3696 169.984-169.3696 444.928 0 614.1952 159.5904 159.6928 411.904 169.4208 582.2464 29.7472l167.0144 167.0144c3.584 3.1744 9.472 3.1744 12.544 0l49.152-49.2032c3.2256-3.072 3.2256-8.9088 0-12.5952z m-257.3824-195.584a347.904 347.904 0 0 1-491.8272 0 347.904 347.904 0 0 1 0-491.8784 347.904 347.904 0 0 1 491.8272 0 347.8528 347.8528 0 0 1 0 491.8784z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--delete-->
<view wx:if="{{name === 'delete'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M316.7232 891.0848c-21.2992 0-39.424-7.5776-54.528-22.6304a74.3424 74.3424 0 0 1-22.5792-54.528V272.384h-42.7008V208.384h192v-37.7344h256v37.7344h192v64h-42.6496v541.5424c0 21.5552-7.4752 39.7824-22.4256 54.784a74.496 74.496 0 0 1-54.7328 22.3744h-400.384zM730.2656 272.384H303.616v541.5424a12.8 12.8 0 0 0 3.6864 9.472 12.8 12.8 0 0 0 9.4208 3.6864h400.384a12.544 12.544 0 0 0 9.0624-4.096 12.544 12.544 0 0 0 4.096-9.0624V272.384z m-324.096 469.3504h64v-384h-64v384z m157.5424 0h64v-384h-64v384z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--arrow_back_ios-->
<view wx:if="{{name === 'arrow_back_ios'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M435.5584 512l252.0064 252.0064-49.5616 49.5616L336.4864 512l301.568-301.568 49.5104 49.5616L435.5584 512z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--contract-->
<view wx:if="{{name === 'contract'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M869.28 94.272V840.64c0 21.76-6.592 40.64-19.84 56.64l-6.048 6.624a85.44 85.44 0 0 1-62.656 25.856H215.392c-21.76 0-40.672-6.592-56.64-19.776l-6.624-6.08c-17.248-17.248-25.856-38.4-25.856-63.264v-134.784H264.96V94.304h604.352zM307.488 705.792h426.688v135.104c0 13.152 4.48 24.096 13.344 32.96 8.928 8.832 19.904 13.312 33.12 13.312 13.056 0 23.968-4.48 32.8-13.376l3.136-3.456a45.44 45.44 0 0 0 10.144-29.76V136.864H307.488v568.96zM168.832 840.64c0 13.248 4.48 24.256 13.344 33.184l3.456 3.104c8.16 6.848 17.728 10.24 28.704 10.24h477.248v-138.72H168.832v92.16z m590.24-422.784v42.56H375.168v-42.56H759.04z m0-128v42.56H375.168v-42.56H759.04z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--jieshu-->
<view wx:if="{{name === 'jieshu'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M341.333333 341.333333m85.333334 0l170.666666 0q85.333333 0 85.333334 85.333334l0 170.666666q0 85.333333-85.333334 85.333334l-170.666666 0q-85.333333 0-85.333334-85.333334l0-170.666666q0-85.333333 85.333334-85.333334Z' fill='{{(isStr ? colors : colors[0]) || 'rgb(44,44,44)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--icon-->
<view wx:if="{{name === 'icon'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M528.96 668.288a24 24 0 0 1-33.92 0L250.368 423.616a24 24 0 0 1 0-33.92l11.264-11.328a24 24 0 0 1 33.92 0L512 594.688l216.384-216.32a24 24 0 0 1 33.92 0l11.328 11.264a24 24 0 0 1 0 33.92l-244.672 244.736z' fill='{{(isStr ? colors : colors[0]) || 'rgb(33,35,34)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--a-Group1561267575-->
<view wx:if="{{name === 'a-Group1561267575'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M0 448h1024v128H0z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M576 0v1024h-128V0z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--content_copy-->
<view wx:if="{{name === 'content_copy'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M384 768a82.176 82.176 0 0 1-60.245333-25.088A82.176 82.176 0 0 1 298.666667 682.666667V170.666667c0-23.466667 8.362667-43.52 25.088-60.245334A82.176 82.176 0 0 1 384 85.333333H768c23.466667 0 43.52 8.362667 60.245333 25.088A82.176 82.176 0 0 1 853.333333 170.666667v512c0 23.466667-8.362667 43.52-25.088 60.245333A82.176 82.176 0 0 1 768 768H384z m0-85.333333H768V170.666667H384v512z m-170.666667 256a82.176 82.176 0 0 1-60.245333-25.088A82.176 82.176 0 0 1 128 853.333333V256h85.333333v597.333333H682.666667v85.333334H213.333333z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--a-bianji11-->
<view wx:if="{{name === 'a-bianji11'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M544.682667 135.338667v58.453333a7.338667 7.338667 0 0 1-7.338667 7.338667H237.738667a36.608 36.608 0 0 0-36.010667 30.037333l-0.597333 6.570667v585.130666a36.608 36.608 0 0 0 30.037333 36.010667l6.570667 0.512h585.130666a36.608 36.608 0 0 0 36.010667-29.952l0.512-6.570667v-317.44a7.338667 7.338667 0 0 1 7.338667-7.338666h58.538666a7.338667 7.338667 0 0 1 7.338667 7.338666v317.44a109.738667 109.738667 0 0 1-99.242667 109.226667l-10.496 0.512H237.738667a109.738667 109.738667 0 0 1-109.226667-99.242667L128 822.869333V237.738667a109.738667 109.738667 0 0 1 99.157333-109.226667l10.581334-0.512h299.605333a7.338667 7.338667 0 0 1 7.338667 7.338667z m307.370666 31.829333l41.386667 41.386667a7.338667 7.338667 0 0 1 0 10.24L438.272 674.133333a7.338667 7.338667 0 0 1-10.24 0l-41.472-41.472a7.338667 7.338667 0 0 1 0-10.24l455.168-455.168a7.338667 7.338667 0 0 1 10.24 0z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--yunshuzhong-->
<view wx:if="{{name === 'yunshuzhong'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1365 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M49.379556 840.362667h49.152a174.990222 174.990222 0 0 1 346.680888 0h345.429334a174.990222 174.990222 0 0 1 346.680889 0h98.531555a49.493333 49.493333 0 0 0 49.379556-49.379556V49.379556A49.493333 49.493333 0 0 0 1235.968 0H444.871111a49.379556 49.379556 0 0 0-49.379555 49.379556v148.366222h-172.942223a93.070222 93.070222 0 0 0-74.296889 49.379555l-110.364444 196.835556c-20.935111 29.468444-34.133333 63.829333-37.888 99.783111v247.239111a49.379556 49.379556 0 0 0 49.379556 49.379556zM102.058667 501.76l103.992889-193.422222a38.684444 38.684444 0 0 1 19.114666-11.719111H345.884444v247.125333H82.830222a91.022222 91.022222 0 0 1 19.228445-41.984z m861.866666 239.729778a123.562667 123.562667 0 1 0 0 247.239111 123.562667 123.562667 0 0 0 0-247.239111z m-691.996444 0a123.562667 123.562667 0 1 0 0 247.239111 123.562667 123.562667 0 0 0 0-247.239111z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--jujue-->
<view wx:if="{{name === 'jujue'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M182.830545 49.803636l789.876364 789.969455-131.630545 131.630545L51.2 181.527273 182.830545 49.803636z' fill='{{(isStr ? colors : colors[0]) || 'rgb(44,44,44)'}}' /%3E%3Cpath d='M972.8 181.527273l-789.969455 789.876363L51.2 839.68 841.076364 49.803636 972.8 181.527273z' fill='{{(isStr ? colors : colors[1]) || 'rgb(44,44,44)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--tijiao-->
<view wx:if="{{name === 'tijiao'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M182.820886 107.501101a107.541698 107.541698 0 0 1 165.829298-90.335026l625.570055 404.356783a107.541698 107.541698 0 0 1 0 180.670052l-625.462514 404.571866a107.563206 107.563206 0 0 1-109.816204 4.15111A107.57396 107.57396 0 0 1 182.820886 916.42975V107.501101z' fill='{{(isStr ? colors : colors[0]) || 'rgb(0,0,0)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--daishenpi-->
<view wx:if="{{name === 'daishenpi'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 4096 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M1024 512a512 512 0 1 1-1024 0 512 512 0 0 1 1024 0zM2560 512a512 512 0 1 1-1024 0 512 512 0 0 1 1024 0zM4096 512a512 512 0 1 1-1024 0 512 512 0 0 1 1024 0z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--a-wupinlingyong21-->
<view wx:if="{{name === 'a-wupinlingyong21'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M179.456 216.832a47.168 47.168 0 0 0-47.232-47.232H96a41.6 41.6 0 0 1 0-83.2h36.224c72.064 0 130.432 58.368 130.432 130.432v513.024h-83.2V216.832z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M502.4 335.168a17.536 17.536 0 0 0-17.6-17.536H224v-83.2h260.8c55.68 0 100.8 45.12 100.8 100.736V483.2h-83.2V335.168z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M733.12 542.4a17.6 17.6 0 0 0-17.6-17.6H224V441.6h491.52c55.68 0 100.8 45.12 100.8 100.8v303.36h-83.2v-303.36z' fill='{{(isStr ? colors : colors[2]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M903.68 812.544l6.656 0.64a33.28 33.28 0 0 1 0 65.28l-6.72 0.64H288v-66.56h615.68z' fill='{{(isStr ? colors : colors[3]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M264.576 805.76a48.64 48.64 0 1 0-97.216 0.128 48.64 48.64 0 0 0 97.28-0.128z m83.2 0a131.84 131.84 0 1 1-263.68 0 131.84 131.84 0 0 1 263.68 0z' fill='{{(isStr ? colors : colors[4]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--a-21-xinhaoqiehuan1-->
<view wx:if="{{name === 'a-21-xinhaoqiehuan1'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M857.472 719.488a130.88 130.88 0 0 1-92.672 38.144h-473.6A130.688 130.688 0 0 1 160 627.008V274.56A130.88 130.88 0 0 1 291.2 144h473.6A130.752 130.752 0 0 1 896 274.624v352.384a130.816 130.816 0 0 1-38.528 92.48z m-33.024-444.928a59.52 59.52 0 0 0-59.648-59.328h-473.6a59.456 59.456 0 0 0-59.648 59.392v352.448a59.392 59.392 0 0 0 59.648 59.328h473.6a59.456 59.456 0 0 0 59.648-59.328V274.56z m-240 331.392H500.288v28.608l-56.32-57.216 56.32-57.216v28.608h84.16c36.224 0 68.288-23.552 79.808-58.56a86.784 86.784 0 0 0-28.992-95.744l0.256-0.384 39.616-40.32a144.896 144.896 0 0 1-9.92 226.24c-23.616 16.768-51.84 25.856-80.768 25.984z m-28.096-228.864H472.192c-36.224 0-68.352 23.552-79.808 58.56-11.52 34.944 0.192 73.472 29.056 95.744l-39.872 40.704a144.896 144.896 0 0 1 9.92-226.176c23.552-16.832 51.776-25.92 80.704-26.048h84.16V291.2l57.472 57.216-57.472 57.216v-28.608z m-267.968 430.08l479.232-0.64a36.224 36.224 0 0 1 36.416 36.288 36.48 36.48 0 0 1-36.352 36.48l-479.36 0.704a36.416 36.416 0 1 1 0-72.832h0.064z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--yixiadan-->
<view wx:if="{{name === 'yixiadan'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M745.472 0H186.368A186.368 186.368 0 0 0 0 185.344v652.288A187.392 187.392 0 0 0 186.368 1024h559.104a186.368 186.368 0 0 0 185.344-186.368V185.344A185.344 185.344 0 0 0 745.472 0zM279.552 371.712h279.552v93.184H279.552v-93.184z m372.736 279.552H279.552v-93.184h372.736v93.184z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--a-Frame427320086-->
<view wx:if="{{name === 'a-Frame427320086'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M512 463.872l249.1392-249.1392a33.9968 33.9968 0 1 1 48.128 48.128L560.0768 512l249.1904 249.1392a33.9968 33.9968 0 0 1-48.128 48.128L512 560.0768l-249.1392 249.1904a33.9968 33.9968 0 0 1-48.128-48.128L463.9232 512 214.7328 262.8608a33.9968 33.9968 0 0 1 48.128-48.128L512 463.9232z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--check-->
<view wx:if="{{name === 'check'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1479 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M485.944889 1024L0 538.055111l133.802667-133.802667 352.142222 352.142223L1242.453333 0l133.802667 133.802667L485.944889 1024z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--lujing-->
<view wx:if="{{name === 'lujing'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M801.792 552.192L227.84 963.584C187.904 992 128 967.68 128 923.264V100.608C128 56.32 188.16 32 227.712 60.416l574.08 411.264a48 48 0 0 1 0 80.64z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--gongzuotai-->
<view wx:if="{{name === 'gongzuotai'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M217.6 422.4h204.8V217.6H217.6v204.8z m256 8.533333l-0.213333 4.309334a42.709333 42.709333 0 0 1-38.144 38.144l-4.352 0.213333H209.066667l-4.352-0.213333a42.709333 42.709333 0 0 1-38.101334-38.144L166.4 430.933333V209.066667a42.666667 42.666667 0 0 1 38.314667-42.453334l4.352-0.213333h221.866666l4.352 0.213333a42.666667 42.666667 0 0 1 38.314667 42.453334v221.866666zM601.6 422.4h204.8V217.6h-204.8v204.8z m256 8.533333l-0.256 4.309334a42.709333 42.709333 0 0 1-38.101333 38.144l-4.352 0.213333h-221.866667l-4.352-0.213333a42.709333 42.709333 0 0 1-38.101333-38.144L550.4 430.933333V209.066667a42.666667 42.666667 0 0 1 38.314667-42.453334l4.352-0.213333h221.866666l4.352 0.213333a42.666667 42.666667 0 0 1 38.314667 42.453334v221.866666zM217.6 806.4h204.8v-204.8H217.6v204.8z m256 8.533333l-0.213333 4.309334a42.709333 42.709333 0 0 1-38.144 38.144l-4.352 0.213333H209.066667l-4.352-0.213333a42.709333 42.709333 0 0 1-38.101334-38.144L166.4 814.933333v-221.866666a42.666667 42.666667 0 0 1 38.314667-42.453334l4.352-0.213333h221.866666l4.352 0.213333a42.666667 42.666667 0 0 1 38.314667 42.453334v221.866666zM601.6 806.4h204.8v-204.8h-204.8v204.8z m256 8.533333l-0.256 4.309334a42.709333 42.709333 0 0 1-38.101333 38.144l-4.352 0.213333h-221.866667l-4.352-0.213333a42.709333 42.709333 0 0 1-38.101333-38.144L550.4 814.933333v-221.866666a42.666667 42.666667 0 0 1 38.314667-42.453334l4.352-0.213333h221.866666l4.352 0.213333a42.666667 42.666667 0 0 1 38.314667 42.453334v221.866666z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--ballot-->
<view wx:if="{{name === 'ballot'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M512 426.666667h205.141333V362.666667H512V426.666667z m0 234.666666h205.141333V597.333333H512v64z m-128-197.76c19.072 0 35.370667-6.698667 48.768-20.138666 13.44-13.397333 20.138667-29.696 20.138667-48.768 0-19.072-6.698667-35.328-20.138667-48.768A66.432 66.432 0 0 0 384 325.76c-19.072 0-35.370667 6.698667-48.768 20.138667a66.474667 66.474667 0 0 0-20.138667 48.768c0 19.072 6.698667 35.370667 20.138667 48.768 13.397333 13.44 29.696 20.138667 48.768 20.138666z m0 234.666667c19.072 0 35.370667-6.698667 48.768-20.138667 13.44-13.44 20.138667-29.696 20.138667-48.768 0-19.072-6.698667-35.370667-20.138667-48.768A66.432 66.432 0 0 0 384 560.426667c-19.072 0-35.370667 6.698667-48.768 20.138666a66.432 66.432 0 0 0-20.138667 48.768c0 19.072 6.698667 35.328 20.138667 48.768 13.397333 13.44 29.696 20.138667 48.768 20.138667zM226.474667 874.666667c-21.546667 0-39.808-7.466667-54.741334-22.4a74.453333 74.453333 0 0 1-22.4-54.741334V226.474667c0-21.546667 7.466667-39.808 22.4-54.741334 14.933333-14.933333 33.194667-22.4 54.741334-22.4h571.050666c21.546667 0 39.808 7.466667 54.741334 22.4 14.933333 14.933333 22.4 33.194667 22.4 54.741334v571.050666c0 21.546667-7.466667 39.808-22.4 54.741334-14.933333 14.933333-33.194667 22.4-54.741334 22.4H226.474667z m0-64h571.050666a12.544 12.544 0 0 0 9.045334-4.096 12.544 12.544 0 0 0 4.096-9.045334V226.474667a12.544 12.544 0 0 0-4.096-9.045334 12.544 12.544 0 0 0-9.045334-4.096H226.474667a12.544 12.544 0 0 0-9.045334 4.096 12.544 12.544 0 0 0-4.096 9.045334v571.050666c0 3.285333 1.365333 6.314667 4.096 9.045334a12.544 12.544 0 0 0 9.045334 4.096z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--gongzuotai-1-->
<view wx:if="{{name === 'gongzuotai-1'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M166.4 209.066667a42.666667 42.666667 0 0 1 42.666667-42.666667h221.866666a42.666667 42.666667 0 0 1 42.666667 42.666667v221.866666a42.666667 42.666667 0 0 1-42.666667 42.666667H209.066667a42.666667 42.666667 0 0 1-42.666667-42.666667V209.066667zM550.4 209.066667a42.666667 42.666667 0 0 1 42.666667-42.666667h221.866666a42.666667 42.666667 0 0 1 42.666667 42.666667v221.866666a42.666667 42.666667 0 0 1-42.666667 42.666667h-221.866666a42.666667 42.666667 0 0 1-42.666667-42.666667V209.066667zM166.4 593.066667a42.666667 42.666667 0 0 1 42.666667-42.666667h221.866666a42.666667 42.666667 0 0 1 42.666667 42.666667v221.866666a42.666667 42.666667 0 0 1-42.666667 42.666667H209.066667a42.666667 42.666667 0 0 1-42.666667-42.666667v-221.866666zM550.4 593.066667a42.666667 42.666667 0 0 1 42.666667-42.666667h221.866666a42.666667 42.666667 0 0 1 42.666667 42.666667v221.866666a42.666667 42.666667 0 0 1-42.666667 42.666667h-221.866666a42.666667 42.666667 0 0 1-42.666667-42.666667v-221.866666z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--a-Frame1561268479-->
<view wx:if="{{name === 'a-Frame1561268479'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M789.333333 149.333333h-554.666666c-46.933333 0-85.333333 38.4-85.333334 85.333334v554.666666c0 46.933333 38.4 85.333333 85.333334 85.333334h554.666666c46.933333 0 85.333333-38.4 85.333334-85.333334v-554.666666c0-46.933333-38.4-85.333333-85.333334-85.333334z m-349.866666 524.8c-12.8 12.8-29.866667 21.333333-46.933334 21.333334-21.333333 0-34.133333-8.533333-51.2-21.333334-12.8-12.8-21.333333-29.866667-21.333333-46.933333 0-21.333333 8.533333-34.133333 21.333333-51.2 12.8-12.8 29.866667-21.333333 46.933334-21.333333 17.066667 0 34.133333 8.533333 46.933333 21.333333 12.8 12.8 21.333333 29.866667 21.333333 51.2 0 17.066667-4.266667 29.866667-17.066666 46.933333z m0-234.666666c-12.8 12.8-29.866667 21.333333-46.933334 21.333333-21.333333 0-34.133333-8.533333-51.2-21.333333-12.8-17.066667-21.333333-29.866667-21.333333-51.2 0-17.066667 8.533333-34.133333 21.333333-46.933334 12.8-12.8 29.866667-21.333333 51.2-21.333333 17.066667 0 34.133333 8.533333 46.933334 21.333333 12.8 12.8 21.333333 29.866667 21.333333 51.2-4.266667 17.066667-8.533333 29.866667-21.333333 46.933334z m281.6 217.6h-204.8v-64h204.8v64z m0-234.666667h-204.8V358.4h204.8v64z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />