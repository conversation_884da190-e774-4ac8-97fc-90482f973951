Component({
  properties: {
    // setting | NMMS | weixuanze1 | checked | sort | a-Rectangle346240777 | Vector | jushou | chushi | yit<PERSON><PERSON> | yiqianshou | yunshuzhong1 | paisongzhong | yilanshou | wentijian | ling | jie | quanxuan | weixuanze | yitongyi | sousuo | delete | arrow_back_ios | contract | jieshu | icon | a-Group1561267575 | content_copy | a-bianji11 | yunshuzhong | jujue | tijiao | daishenpi | a-wupinlingyong21 | a-21-xinhaoqiehuan1 | yixiadan | a-Frame427320086 | check | lujing | gongzuotai | ballot | gongzuotai-1 | a-Frame1561268479
    name: {
      type: String,
    },
    // string | string[]
    color: {
      type: null,
      observer: function(color) {
        this.setData({
          colors: this.fixColor(),
          isStr: typeof color === 'string',
        });
      }
    },
    size: {
      type: Number,
      value: 14,
      observer: function(size) {
        this.setData({
          svgSize: size / 750 * wx.getSystemInfoSync().windowWidth,
        });
      },
    },
  },
  data: {
    colors: '',
    svgSize: 14 / 750 * wx.getSystemInfoSync().windowWidth,
    quot: '"',
    isStr: true,
  },
  methods: {
    fixColor: function() {
      var color = this.data.color;
      var hex2rgb = this.hex2rgb;

      if (typeof color === 'string') {
        return color.indexOf('#') === 0 ? hex2rgb(color) : color;
      }

      return color.map(function (item) {
        return item.indexOf('#') === 0 ? hex2rgb(item) : item;
      });
    },
    hex2rgb: function(hex) {
      var rgb = [];

      hex = hex.substr(1);

      if (hex.length === 3) {
        hex = hex.replace(/(.)/g, '$1$1');
      }

      hex.replace(/../g, function(color) {
        rgb.push(parseInt(color, 0x10));
        return color;
      });

      return 'rgb(' + rgb.join(',') + ')';
    }
  }
});
