/* tslint:disable */
/* eslint-disable */

import React, { FunctionComponent } from 'react';
import Taro from '@tarojs/taro';

export type IconNames = 'setting' | 'NMMS' | 'weixuanze1' | 'checked' | 'sort' | 'a-Rectangle346240777' | 'Vector' | 'jushou' | 'chushi' | 'yituihui' | 'yiqianshou' | 'yunshuzhong1' | 'paisongzhong' | 'yilanshou' | 'wentijian' | 'ling' | 'jie' | 'quanxuan' | 'weixuanze' | 'yitongyi' | 'sousuo' | 'delete' | 'arrow_back_ios' | 'contract' | 'jieshu' | 'icon' | 'a-Group1561267575' | 'content_copy' | 'a-bianji11' | 'yunshuzhong' | 'jujue' | 'tijiao' | 'daishenpi' | 'a-wupinlingyong21' | 'a-21-xinhaoqiehuan1' | 'yixiadan' | 'a-Frame427320086' | 'check' | 'lujing' | 'gongzuotai' | 'ballot' | 'gongzuotai-1' | 'a-Frame1561268479';

interface Props {
  name: IconNames;
  size?: number;
  color?: string | string[];
  style?: React.CSSProperties;
}

const IconFont: FunctionComponent<Props> = (props) => {
  const { name, size, color, style } = props;

  // @ts-ignore
  return <iconfont name={name} size={parseFloat(Taro.pxTransform(size))} color={color} style={style} />;
};

IconFont.defaultProps = {
  size: 14,
};

export default IconFont;
