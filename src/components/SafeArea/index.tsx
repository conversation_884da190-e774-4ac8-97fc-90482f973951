import { observer } from 'mobx-react'

import { useFixedHeight } from '@/hook'

import { ReactNode } from 'react'
import FixedView from '../FixedView'
import './index.scss'
import { ZIndex } from '@/constants/zIndex'
import classNames from 'classnames'

export interface SafeAreaProps {
  children: ReactNode
  className?: string
  position?: 'top' | 'bottom' | 'center'
  //到对应position边的最小距离 center不适用
  minDistance?: number
  //全屏zindex 9990 高层组件 如TabBar HeaderBar
  isFullScreen?: boolean
  //普通组件
  isComp?: boolean
  style?: any
  zIndex?: ZIndex
  level?: number //是第几级的fixed组件
  [key: string]: any
}
const SafeArea: React.FC<SafeAreaProps> = (props) => {
  const {
    fixedPlaceholderHeight: { topLevel = [], bottomLevel = [] },
  } = useFixedHeight()
  const {
    children,
    className,
    position,
    minDistance = 0,
    isFullScreen,
    isComp,
    style,
    zIndex,
    level = 1,
    ...restProps
  } = props
  const _style = { ...style }
  if (position === 'bottom') {
    _style.bottom = Math.max(bottomLevel[level], minDistance)
  } else if (position === 'top') {
    _style.top = Math.max(topLevel[level], minDistance)
  }
  return (
    <FixedView
      zIndex={isComp ? ZIndex.LowComp : isFullScreen ? ZIndex.HighToast : zIndex}
      position={position}
      className={classNames('safe_area', `safe_area_${position}`, className)}
      style={_style}
      {...restProps}
    >
      {children}
    </FixedView>
  )
}
SafeArea.defaultProps = {
  position: 'bottom',
  isFullScreen: true,
  zIndex: ZIndex.LowToast,
}
export default observer(SafeArea)
