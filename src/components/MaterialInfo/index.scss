.materialInfo {
  @mixin flex {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .topRow {
    @include flex;
    padding: 20px 16px 0;

    .title {
      font-size: 14px;
      font-weight: 500;
      line-height: 20px;
    }
  }

  .material {
    @include flex;
    padding: 0 16px;
    box-sizing: border-box;
    margin-top: 20px;

    .materialImg {
      width: 90px !important;
      height: 90px !important;
      margin-right: 8px;
    }

    .materialText {
      flex: 1;
      width: calc(100% - 90px - 8px);
      text-align: left;

      .materialName {
        font-size: 14px;
        font-weight: 500;
        line-height: 20px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        text-align: left;
      }

      .sku {
        margin-top: 4px;
        font-size: 12px;
        line-height: 16px;
        color: #707070;
        text-align: left;
        background-color: #f6f6f6;
        border-radius: 4px;
        // display: flex;
        // justify-content: center;
        // align-items: center;
        padding: 6px 8px;
        box-sizing: border-box;
        display: inline-block;
      }

      .materialPrice {
        margin-top: 26px;
        font-size: 14px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .returnNumber {
          color: #707070;
        }
      }
    }
  }

  .materialIntroduce {
    font-size: 12px;
    line-height: 16px;
    color: #707070;
    margin-top: 20px;
    padding: 0 16px;
    text-align: left;
  }

  .bottomContent {
    box-shadow: 0px 0px 12px 0px #00000014;
    padding-top: 12px;
    margin-top: 24px;

    .addNumber {
      padding: 0 16px;

      @include flex;

      .numberName {
        font-size: 14px;
        line-height: 20px;
        color: #111;
      }

      .inputNumber {
        border: 1px solid #dedede;
        border-radius: 16px;
        --nutui-inputnumber-input-background-color: transparent;
        --nutui-inputnumber-input-height: 32px;
        --nutui-inputnumber-input-font-color: #000;
        --nutui-inputnumber-input-font-size: 12px;
        --nutui-inputnumber-button-width: 40px;
        --nutui-inputnumber-button-height: 32px;
        .nut-icon {
          font-size: 20px;
          width: 20px;
          height: 20px;
        }
        .nut-number-input {
          font-size: 14px;
        }
      }
    }

    .materialBtn {
      margin-top: 12px;

      .btn {
        width: calc(100% - 32px);
        height: 40px;
        margin: auto;
        border-radius: 4px;
        background: #f39800 !important;
        font-size: 14px;
        color: #fff;
      }

      .notUseBtn {
        background: #bebebe !important;
      }
    }
  }
}
