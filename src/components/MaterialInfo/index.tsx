import { View } from '@tarojs/components'
import { useEffect, useState } from 'react'
import { Popup, SafeArea, Image, InputNumber, Button } from '@nutui/nutui-react-taro'
import { getNavBarInfo } from '@/utils'
import MInputNumber from '../MInputNumber'
import IconFont from '../iconfont'
import './index.scss'

export interface GoodType {
  goods_name: string
  goods_price: string
  img_url: string
  barcode: string
}
export type CurrentGoodType = Partial<
  GoodType & {
    kysl: string | number
    sl?: number
    dgh_sl?: number
    color_name: string
    size_name: string
    spms: string
  }
>

interface MaterialInfoType {
  type: number
  visible: boolean
  typeName: string
  info: CurrentGoodType
  checklist: CurrentGoodType[]
  onSure: (number) => void
  onClose: () => void
}

const MaterialInfo: React.FC<MaterialInfoType> = ({
  visible,
  type,
  typeName,
  info,
  checklist,
  onSure,
  onClose,
}) => {
  const [number, setNumber] = useState(1)
  const [canUsedNumber, setCanUsedNumber] = useState(0)
  const { ios, bottomBarHeight } = getNavBarInfo()
  useEffect(() => {
    setNumber(1)
  }, [visible])

  useEffect(() => {
    if (!Object.keys(info).length) return

    const res = checklist.find((item) => item.barcode == info.barcode)

    const hasUsedNumber = res ? res.sl ?? 0 : 0

    let amount = 0
    if (type == 2 || type == 3) {
      amount = Number(info.dgh_sl) - hasUsedNumber
    } else {
      amount = Number(info.kysl) - hasUsedNumber
    }

    setCanUsedNumber(amount)
  }, [checklist, info, type])

  const onConfirm = () => {
    if (canUsedNumber <= 0) {
      return
    } else {
      const num = number ? number : 1
      onSure({ ...info, sl: num })
    }
  }

  return (
    <View className='materialInfo'>
      <Popup
        zIndex={9990}
        visible={visible}
        position='bottom'
        onClose={() => {}}
        lockScroll
        closeOnOverlayClick={false}
      >
        <View className='topRow'>
          <View className='title'>添加至{typeName}</View>
          <View className='closeIcon' onClick={onClose}>
            <IconFont name='a-Frame427320086' size={20} />
          </View>
        </View>
        <View className='material'>
          <Image className='materialImg' src={info.img_url} />
          <View className='materialText'>
            <View className='materialName'>{info.goods_name}</View>
            <View className='sku'>
              {info.color_name ? info.color_name + '/' : ''}
              {info.size_name}
            </View>

            <View className='materialPrice'>
              <View className='price'>¥{info.goods_price}</View>
              <View className='returnNumber'>
                {[2, 3, '2', '3'].includes(type) ? '待归还数' : '可用库存'}：{canUsedNumber}
              </View>
            </View>
          </View>
        </View>
        <View className='materialIntroduce'>{info.spms}</View>
        <View className='bottomContent'>
          <View className='addNumber'>
            <View className='numberName'>数量</View>
            {/* <InputNumber
              defaultValue={1}
              className='inputNumber'
              min={1}
              max={canUsedNumber}
              value={number}
              onChange={(v) => setNumber(v)}
            /> */}
            <MInputNumber
              defaultValue={1}
              min={1}
              max={canUsedNumber}
              value={number}
              onInput={(v) => {
                setNumber(v)
              }}
            />
          </View>
          <View className='materialBtn'>
            <Button
              type='info'
              className={['btn', canUsedNumber <= 0 ? 'notUseBtn' : ''].join(' ')}
              onClick={onConfirm}
            >
              加入{typeName}
            </Button>
          </View>
          <View style={{ height: bottomBarHeight + 'px' }}></View>
        </View>
      </Popup>
    </View>
  )
}

export default MaterialInfo
