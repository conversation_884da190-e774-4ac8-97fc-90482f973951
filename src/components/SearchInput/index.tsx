import { View } from "@tarojs/components";
import {  Input } from '@nutui/nutui-react-taro'

import React,{useState} from "react";
import IconFont from "../iconfont";
import './index.scss'


interface SearchInputType  {
  placeholder: string;
  onChange: (v:string) => void;
  onConfirm?: (v:string) => void;
  confirmType?:'search'
  children?:React.ReactNode
}


const SearchInput:React.FC<SearchInputType> = ({placeholder,onChange,confirmType="search",onConfirm=()=>{}}) => {
  const [val, setVal] = useState('')
  
 
  return (
    <View className='searchArea'>
      <View className='searchIcon'>
        <IconFont name='Search' size={24} />
      </View>
      <Input type='text' placeholder={placeholder}  value={val}  onChange={setVal} confirmType={confirmType} onConfirm={(e)=> {onConfirm(e.detail.value)}} />
      <View className='line' onClick={() => onChange(val)}>搜索</View>
    </View>
  )

}

export default SearchInput
