import { View } from '@tarojs/components'
import classNames from 'classnames'
import { observer } from 'mobx-react'
import React, { forwardRef, ReactNode, useEffect, useImperativeHandle, useRef } from 'react'

import { Loading, TabBar, HeaderBar } from '@/components'
import { TabbarProps } from '@/components/Tabbar'
import { HeaderBarProps } from '@/components/HeaderBar'
import MainLayoutContext from '@/contexts/mainLayout'
import {  useStores, useTaroSid, useWatchFixed } from '@/hook'
import { ShareAppMessageReturn, useDidHide } from '@tarojs/taro'
import { Toast } from '@nutui/nutui-react-taro'
import { menuButtonReact,systemInfo } from '@/utils'

import './index.scss'


//加载优化选项
interface InitOptions {
  //是否可以加载显示
  inited: boolean
  //加载完过一些时间再展示 优化体验
  initDelay?: number
  //未加载及Delay时是否继续展示loading
  initLoading?: boolean
  //是否全屏
  loadingFullScreen?: boolean
}

interface MainLayoutProps {
  showHeaderBar?: boolean
  headBarConfig?: HeaderBarProps
  showTabBar?: boolean
  tabbarConfig?: TabbarProps
  showTopPlaceholder?: boolean
  showBottomPlaceholder?: boolean
  //断网后刷新触发 一般不需要传
  onErrorRefresh?: () => void
  shareAppMessage?: ShareAppMessageReturn
  //加载优化选项
  initOptions?: Partial<InitOptions>
  //是否需要静默登录 false代表允许游客模式
  needLogin?: boolean
  children?: ReactNode
  className?: string
  bgColor?: string
  style?: React.CSSProperties
}

const MainLayout = forwardRef((props: MainLayoutProps, ref) => {
  
  const { loadingStore, toastStore } = useStores()
  //监听tabbar header等fixed
  //统一管理placeholder防止多个dix计算困难
  const { fixedPlaceholderHeight } = useWatchFixed()
  
  // console.log('------systemInfo',systemInfo);
  // const tabBarHeight = systemInfo.screenHeight -systemInfo.safeArea.bottom + 54
  // console.log('----tabBarHeight',tabBarHeight);
  
  
  
  const { ref: layoutRef, select, selectAll } = useTaroSid()

  //初始化完成后再将页面可见 还可以设置delay防止图片多变形太大
  const initLoadingUUID = useRef<string>('')
  useEffect(() => {
    const { inited, initDelay, initLoading, loadingFullScreen } = {
      inited: true,
      initDelay: 0,
      initLoading: false,
      loadingFullScreen: false,
      ...props.initOptions,
    }
    
    if (inited) {
      if (initDelay) {
        initLoading && loadingStore.close(initLoadingUUID.current)

      } else {
        initLoading && loadingStore.close(initLoadingUUID.current)
      }
    } else {
      initLoading &&
        (initLoadingUUID.current = loadingStore.open({
          isFullScreen: loadingFullScreen,
          isInitLoading: true,
        }))
      // setMainLayoutVisible(false)
    }
  }, [props.initOptions?.inited])

  useEffect(() => {
    return () => {
      loadingStore.close(initLoadingUUID.current)
    }
  }, [])

  const initError = (err) => {
    loadingStore.close(initLoadingUUID.current)
    toastStore.show(err || '加载失败')
  }

  // 简单自定义toast
  const commonFunction = {
    select,
    selectAll,
    initError,
  }

  useImperativeHandle(ref, () => commonFunction)

  const style = Object.assign({}, props.style)

  const ToastNode = () => {
    return (
      <Toast
        visible={toastStore.showToast}
        content={toastStore.toastContent}
        size='small'
        closeOnOverlayClick
      />
    )
  }

  return (
    <View
      className={classNames('main_layout')}
      ref={layoutRef}
      style={
        {
          ...style,
          '--headerbar-height': fixedPlaceholderHeight.top + 'px',
        } as React.CSSProperties
      }
    >
      {props.showTopPlaceholder ? <View style={{ height: fixedPlaceholderHeight.top }} /> : null}
      {props.showHeaderBar ? <HeaderBar {...props.headBarConfig} /> : null}
      {/* <Loading /> */}
      {/* 简单自定义toast */}
      {ToastNode()}
      {/* 静默登录的页面在静默登录之前都不要展示 但还是需要配合useSlientAuthEffect才能保证执行顺序 */}
      <View className={classNames('main_layout_content', props.className)}>
        <MainLayoutContext.Provider value={commonFunction}>
          {props.children}
        </MainLayoutContext.Provider>
      </View>
      {props.showTabBar ? <TabBar {...props.tabbarConfig} /> : null}
    </View>
  )
})

MainLayout.defaultProps = {
  showHeaderBar: true,
  showTabBar: true,
  showTopPlaceholder: true,
  showBottomPlaceholder: true,
  needLogin: true,
}

export default observer(MainLayout)
