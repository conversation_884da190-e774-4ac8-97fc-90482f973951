import { useStores } from '@/hook'
import { Button, View } from '@tarojs/components'
import { observer } from 'mobx-react'
import { Image } from '@nutui/nutui-react-taro'
import Taro from '@tarojs/taro'
import './index.scss'

const NeedLogin = (props) => {
  const { userStore } = useStores()
  const {
    onClick: _onClick,
    onLogin,
    children,
    needLogin = true,
    // 微信订阅消息不可以放在回调
    // 判断下如果是注册的 就不触发 需要二次点击
    needSubscribe = false,
    disabled,
    ...restProps
  } = props


  const handleGetPhoneNumber = (e: any) => {
    const { encryptedData, iv } = e.detail
    if (encryptedData && iv) {
      userStore.authLogin(e.detail).then((res: any) => {
        console.log('----res-----', res)
        if(res.code == 500) {
          Taro.showToast({
            icon:'none',
            title: res.msg,
            duration: 2000

          })

        }else {
          _onClick?.()
        }
      })
    }
  }

  return (
    <View>
      <Image className='baiqiuImg' src='https://bq-oms-prod.oss-cn-zhangjiakou.aliyuncs.com/mms/images/noLogin.png?OSSAccessKeyId=LTAI5t6xTosFuZ2TyQuE1EWQ&Expires=1905911817&Signature=%2BBtJPQrL5McrqqpARfCOGjw5Myw%3D' />
      <Button
        openType='getPhoneNumber'
        onGetPhoneNumber={(e) => handleGetPhoneNumber(e)}
        disabled={disabled}
        {...restProps}
      >

        {children}
      </Button>
    </View>
  )
}
export default observer(NeedLogin)
