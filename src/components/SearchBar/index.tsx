import { SearchBar as NutuiSearchBar, Button } from '@nutui/nutui-react-taro'
import { useState } from 'react'
import { View, Input } from '@tarojs/components'
import IconFont from '@/components/Iconfont'
import './index.scss'

interface SearchBarType {
  focus:boolean
  disabled: boolean
  onSearch?: (v: string) => void
}

const SearchBar: React.FC<SearchBarType> = ({ focus=true,onSearch, disabled = false }) => {
  const [value, setValue] = useState('')
  return (
    <View className='searchBar'>
      <View className='searchIcon'>
        <IconFont name='sousuo' size={18} color='#a7a7a7' />
      </View>
      <Input placeholder='请输入物料名称/SKU查询' focus={focus} value={value} type='text' confirmType='search' 
        onInput={(e) => {
          setValue(e.detail.value)
          if (!e.detail.value) onSearch && onSearch('')
        }} 
        disabled={disabled} 
        onConfirm={() => {
        onSearch && onSearch(value)
      }}
      />

      <Button
        className='searchButton'
        onClick={() => {
          console.log('---value', value);

          onSearch && onSearch(value)
        }}
      >
        搜索
      </Button>
    </View>
  )
}

export default SearchBar
