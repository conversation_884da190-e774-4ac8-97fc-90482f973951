import { MovableView, View } from '@tarojs/components'

import IconFont from '@/components/iconfont/index.weapp'
import './index.scss'

interface ScanQrCodeProps {
  onClick?: () => void
}

const ScanQrCode = (props) => {
  return (
    // <MovableView
    //   className='com_movableview_scan'
    //   direction='all'
    //   inertia={false}
    //   onClick={props?.onClick ? props.onClick : undefined}
    // >
    <View
      className='com_movableview_scan'
      onClick={() => {
        props.onScanBtnClick()
      }}
    >
      <IconFont name='a-saoyisao1' size={46} color='#ffffff' />
    </View>
    // </MovableView>
  )
}

export default ScanQrCode
