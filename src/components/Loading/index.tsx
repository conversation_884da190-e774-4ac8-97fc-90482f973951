import { View } from '@tarojs/components'
import { getCurrentInstance } from '@tarojs/runtime'
import classNames from 'classnames'
import { observer } from 'mobx-react'
import { useEffect, useRef, useState } from 'react'

import { useStores } from '@/hook'
import Taro from '@tarojs/taro'
import './index.scss'
import { Refresh } from '@nutui/icons-react-taro'

const Loading: React.FC<LoadingProps> = (props) => {
  const { loadingStore } = useStores()
  const registerPagePath = useRef<any>('')
  const [isShow, setIsShow] = useState<boolean>(true)
  useEffect(() => {
    
    const pageInstance = getCurrentInstance()
    const currentPath = pageInstance?.router?.$taroPath
    registerPagePath.current = currentPath

  }, [])

  useEffect(() => {
    const pageInstance = getCurrentInstance()
    const currentPath = pageInstance?.router?.$taroPath
    
    
    
    setIsShow(currentPath === registerPagePath.current && loadingStore.isShow)
  }, [loadingStore.isShow])

  useEffect(() => {
    
    if (isShow) {
      Taro.showLoading({
        title: '加载中...',
      })
    } else {
      Taro.hideLoading()
    }
  }, [isShow])

  //hide的时候不要去除元素 免得重绘页面导致image要重新计算高度导致闪烁
  return <View className='loading_test'></View>
}

export default observer(Loading)
