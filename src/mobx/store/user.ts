import Taro from '@tarojs/taro'
import { makeAutoObservable } from 'mobx'
import http from '@/http'
import { makePersistable } from 'mobx-persist-store'
import { md5 } from 'js-md5'
import { CrmUserInfo, UserInfoType } from '../model/UserInfo.ts'

// import toastStore from './toast'

class UserStore {
  constructor() {
    makeAutoObservable(this)
    makePersistable(this, {
      name: 'UserStore',
      properties: ['userInfo'],
      expireIn: 0,
    }).then()
  }

  //是否全局静默登录过一次刷新过身份
  isInitAuthed: boolean = false
  userInfo: Partial<UserInfoType> = {}
  crmUserInfo: CrmUserInfo
  customerInfoData: any

  //注册后的跳转页地址
  afterLoggedUrl: string = ''
  afterLoggedCallback: Array<any> = []

  //重新getToken中发出的请求等待队列
  isRefreshingToken: boolean = false
  refreshTokenWaitingList: Array<any> = []

  // 登录页相关参数
  showPasswordErr: boolean = false

  // 门店列表
  storeList: any = []

  //是否注册并登录
  get isLogin() {
    return !!this.userInfo?.token
  }
  /**
   * 获取手机号 取得token,并查询该用户是有有审批权限
   */
  async authLogin(params) {
    try {
      const { code } = params
      const res = await http.login.login({ code })
      console.log('-----token', res?.data?.access_token)

      this.userInfo.token = res?.data?.access_token
      this.userInfo.mobile = res?.data?.mobile
      this.userInfo.tokenExpireTime = res?.data?.expires
      this.userInfo.userName = res?.data?.user_name
      this.userInfo.salesBrand = res?.data?.sales_brand
      this.userInfo.logoUrl = res?.data?.logo_url

      http.common.default.post({}, {}, '?c=zdwld&m=spqx').then((v) => {
        this.userInfo.approve = v?.data?.spqx
      })

      return res
    } catch (e) {
      console.error('手机号快速验证失败', e)
      this.userInfo.token = ''
      this.userInfo.mobile = ''
      return Promise.reject(e)
    }
  }
  setUserInfo(params) {
    this.userInfo = { ...this.userInfo, ...params }
  }

  async loginBtnClick({ username, password, remember }) {
    console.log('loginBtnClick', { username, password, remember })

    if (username == '') {
      Taro.showToast({
        title: '请输入账号',
        icon: 'none',
        duration: 2000,
      })
      return new Promise((_resolve, reject) => {
        reject({ code: '200', status: false })
      })
    }
    if (password == '') {
      Taro.showToast({
        title: '请输入密码',
        icon: 'none',
        duration: 2000,
      })
      return new Promise((_resolve, reject) => {
        reject({ code: '200', status: false })
      })
    }

    console.log({
      username: md5(username),
      password: md5(password),
      sales_brand: 'subdued',
    })

    try {
      const req: any = await http.login.login({
        // sales_brand:'subdued'
        // user: SUBDUED01
        // pass: 123456
        username: md5(username),
        password: md5(password),
        sales_brand: 'subdued',
      })
      if (req.code == 0 && req.data?.access_token) {
        // debugger
        Taro.setStorageSync('token', req.data?.access_token)
        Taro.setStorageSync('tokenExpireTime', parseInt(req.data?.expires) * 1000)

        // 门店列表
        this.storeList = [
          {
            sales_brand: 'smcp_nmms',
            logo_url: null,
          },
          {
            sales_brand: 'wuliao',
            logo_url: null,
          },
        ] //req.data?.brands

        if (remember) {
          this.userInfo = {
            username: username,
            password: password,
            remember: true,
            token: req.data?.access_token,
            tokenExpireTime: req.data?.expires ? req.data?.expires * 1000 : 0,
          }
          console.log('this.userInfo1111111', this.userInfo)
        } else {
          this.userInfo = {
            username: undefined,
            password: undefined,
            remember: false,
            token: req.data?.access_token,
            tokenExpireTime: req.data?.expires ? req.data?.expires * 1000 : 0,
          }
          console.log('this.userInfo2222222', this.userInfo)
        }
        Taro.switchTab({ url: '/pages/home/<USER>' })
      } else {
        console.log(22222222244)

        this.userInfo.token = ''
        this.showPasswordErr = true
        Taro.setStorageSync('token', '')
      }
    } catch (e) {
      Taro.showToast({
        title: '网络错误，请稍后再试',
        icon: 'none',
        duration: 2000,
      })
      console.error(e)
    }
  }
}

const userStore = new UserStore()
export default userStore
