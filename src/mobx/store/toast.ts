import { makeAutoObservable } from 'mobx'
import Taro from '@tarojs/taro'

class ToastStore {
  constructor() {
    makeAutoObservable(this)
  }
  //标准Toast
  showToast: boolean = false
  toastContent: string = ''

  //标准Toast
  show(content: string) {
    // this.toastContent = content
    // this.showToast = true
		Taro.showToast({
			title: content,
			icon: 'none',
			duration: 2000
		})
    // setTimeout(() => {
    //   this.hide()
    // }, 200000)
  }

  hide() {
    this.showToast = false
  }
}

const toastStore = new ToastStore()

export default toastStore
