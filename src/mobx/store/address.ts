import Taro from '@tarojs/taro'
import { makeAutoObservable } from 'mobx'

import { goodType } from '@/components/ProductList'
import http from '@/http'

export interface MenuType {
  category_code: string
  category_name: string
}

class AddressStore {
  constructor() {
    makeAutoObservable(this)
  }
  addressList: []
  setAddressList(v) {
    this.addressList = v
  }
  
}

const addressStore = new AddressStore()
export default addressStore
