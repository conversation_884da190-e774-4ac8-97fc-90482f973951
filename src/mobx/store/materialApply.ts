import Taro from '@tarojs/taro'
import { makeAutoObservable } from 'mobx'
import { makePersistable } from 'mobx-persist-store'

import { goodType } from '@/components/ProductList'
import http from '@/http'

export interface MenuType {
  category_code: string
  category_name: string
}

class MaterialApplyStore {
  constructor() {
    makeAutoObservable(this)
    // makePersistable(this, {
    //   name: 'CommonStore',
    //   properties: ['quickRegisterHide'],
    //   expireIn: 0,
    // })
  }
  goodsList = []
  type: 0 | 1 = 0 // 领用/借用
  selectedMenuValue = '' // 选中的菜单
  selectedGoodsList: goodType[] = [] // 选中的商品
  menus: MenuType[] = []
  checkedGoodsList: goodType[] = [] // 待提交商品

  goodList: goodType[] = []
  baseInfo = {}



  setType(value: 0 | 1) {
    this.type = value
    this.getMenus(value)
  }
  async getMenus(value: 0 | 1) {
    this.menus = []
    const res = await http.common.default.post({ djlx: value }, {}, '?c=category&m=list')
    if (res.code == 0) {
      this.menus = res.data.list
      
      this.setSelectedMenuValue(res.data.list[0]?.['category_code'])

    }
  }
  async getGoodsList(value: string) {
    this.goodsList= []
    const res = await http.common.default.post({ djlx: this.type,category_code:value,loading:true }, {}, '?c=barcode&m=list')
    if (res.code == 0) {
      this.goodsList = res?.data?.list
    }
  }

  setSelectedMenuValue(value: string) {
    this.selectedMenuValue = value
    this.getGoodsList(value)
  }
  setSelectedGoodsList(value: goodType[]) {
    this.selectedGoodsList = value
  }
  setCheckedGoodsList(value: goodType[]) {
    this.checkedGoodsList = value
  }
  setGoodList(value: goodType[]) {
    this.goodList = value

  }
  setBaseInfo(params: object) {
    this.baseInfo  = params
  }

}

const materialApplyStore = new MaterialApplyStore()
export default materialApplyStore
