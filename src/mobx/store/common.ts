
import Taro from '@tarojs/taro'
import { md5 } from 'js-md5'
import { makeAutoObservable } from 'mobx'
import { makePersistable } from 'mobx-persist-store'
import QS from 'qs'
export interface FixedHeight {
  header: number
  tabbar: number
  headerL2: number
  tabbarL2: number
}

export interface FixedPlaceholderHeightObj {
  top: number
  bottom: number
  topLevel: Array<number>
  bottomLevel: Array<number>
}
class CommonStore {
  constructor() {
    makeAutoObservable(this)
    makePersistable(this, {
      name: 'CommonStore',
      properties: ['quickRegisterHide'],
      expireIn: 0,
    })
  }
  isNetworkOK = true
  //关闭快捷登录时间
  quickRegisterHide: number | null = null
  //记录fix组件高度
  //添加组件后记得添加fixedPlaceholderHeight
  //如果添加其他组件 可以增加topL2类似来计算对应级别的top
  // topL2 = topL1的高度 L1应该只有header
  fixedHeight: FixedHeight = {
    header: 0, //L1
    tabbar: 0, //L1
    headerL2: 0, //L2
    tabbarL2: 0, //L2
  }
  //加算fix组件高度
  get fixedPlaceholderHeight(): FixedPlaceholderHeightObj {
    let { header, tabbar, headerL2, tabbarL2 } = this.fixedHeight
    return {
      //供普通组件使用
      top: header + headerL2,
      bottom: tabbar + tabbarL2,
      //如果添加其他组件 可以增加topL2类似来计算对应级别的top
      // topL2 = topL1的高度 L1应该只有header
      // top = topL1的高度+topL2的高度
      topLevel: [0, 0, header],
      bottomLevel: [0, 0, tabbar],
    }
  }

  historyListMax: number = 10
  //从前往后插入元素
  historyList: Array<string> = []
  pushHistory(item: string) {
    // 如果队列已满，先移除最后一个元素
    if (this.historyList.length >= this.historyListMax) {
      this.historyList.pop()
    }
    // 然后在队列的最前面添加新元素
    this.historyList.unshift(item)
  }

  //oss配置json
  commonConfig: { [x: string]: any } = {}


  // 提前缓存Http请求
  requestCache: { [x: string]: any } = {}
  setRequestCache(params: any, value: any) {
    let key = md5(QS.stringify({ ...params, ccs_rc_timestamp: Date.now() }))
    this.requestCache[key] = value
    return key
  }
  //为了保证缓存太久过期 仅可使用一次
  getRequestCache(key: string, deleteValue = true) {
    let ret = this.requestCache[key]
    if (deleteValue) {
      delete this.requestCache[key]
    }
    return ret
  }
}

const commonStore = new CommonStore()
export default commonStore
