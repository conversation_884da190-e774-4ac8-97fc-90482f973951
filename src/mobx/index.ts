//全局配置
//不可以直接写这里 否则store的构造函数会先执行
import './config'

export { default as commonStore } from './store/common'
export { default as loadingStore } from './store/loading'
export { default as toastStore } from './store/toast'
export { default as userStore } from './store/user'
export {default as materialStore} from './store/material'
export {default as materialRepayStore} from './store/materialRepay'
export {default as materialAddStore} from './store/materialAdd'
export {default as materialApplyStore} from './store/materialApply'
export {default as addressStore} from './store/address'


