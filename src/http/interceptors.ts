import Taro from '@tarojs/taro'
import { cloneDeep } from 'lodash'

import {
  commonStore,
  loadingStore,
  toastStore,
  userStore,
  materialStore,
  materialRepayStore,
} from '@/mobx'

////处理无token 或token过期
const handleToken = (requestParams) => {
  if (requestParams?.Authorization == false) {
    return true
  }
  let { token, tokenExpireTime } = userStore.userInfo

  if (!token || token == '') {
    return false
  }

  if (!tokenExpireTime) {
    return false
  }
  // console.log('-------(Date.now()',(Date.now()));
  // console.log('-------(Date.tokenExpireTime()',tokenExpireTime);

  // if (tokenExpireTime && tokenExpireTime < dayjs(Date.now()).valueOf()) {
  //   Taro.showToast({
  //     title: '登录已过期,请重新登录',
  //     icon: 'none',
  //   })
  //   userStore.setUserInfo({
  //     token: '',
  //     mobile: '',
  //     openId: '',
  //     userName: '',
  //   })
  //   return false
  // }
  return true
}

//处理头部添加token
const handleAuthorization = (requestParams) => {
  if (requestParams?.Authorization) {
    switch (requestParams?.AuthorizationType) {
      default:
        {
          let { token } = userStore.userInfo
          if (!token) {
            token = Taro.getStorageSync('token')
          }
          if (token) {
            requestParams.header.Authorization = 'Bearer ' + token
          }
        }
        break
    }
  }
  return requestParams
}

const handleHeader = (requestParams: any) => {
  if (!requestParams.header) {
    requestParams.header = {}
  }
  requestParams.header['m2-present'] = commonStore.historyList[0] || ''
  requestParams.header['m2-forward'] = commonStore.historyList[1] || ''
}

//最多重新请求1次
const maxResendTime = 1
// 请求拦截
export const interceptor = (chain) => {
  // 获取参数签名
  const requestParams = chain.requestParams

  const requestParamsBackup = cloneDeep(requestParams)
  //重发次数限制
  requestParamsBackup.resendTime = (requestParamsBackup.resendTime || 0) + 1
  const api = requestParams

  let loadingUUID
  if (api?.loadingFullScreen || api?.loading) {
    loadingUUID = loadingStore.open({
      isFullScreen: api?.loadingFullScreen,
      //如果是小于500ms的快接口就没必要loading了
      showDelay: api.showDelay || 500,
    })
  }

  // 获取网络状态
  Taro.getNetworkType({
    // 获取成功
    success: (res) => {
      // 网络状态
      const hasWeb = res.networkType === 'none'
      // 修改网络状态
      commonStore.isNetworkOK = !hasWeb
      // 如果没有网，取消所有loading
      if (hasWeb) {
        loadingStore.clear()
      }
    },
    // 获取失败
    fail: () => {},
  })
  const validToken = handleToken(requestParams)

  if (!validToken) {
    // 不可以使用redirectTo, 否则报跳转超时错误，阻止页面渲染,
    // Taro.reLaunch({ url: '/pages/home/<USER>' })

    const routes = Taro.getCurrentPages()
    const currentRoute = routes[routes.length - 1].route ?? ''
    if (!['pages/home/<USER>', 'pages/material/index'].includes(currentRoute)) {
      Taro.reLaunch({ url: '/pages/home/<USER>' })
    }

    loadingStore.clear()
    return Promise.resolve(new Error('请求已取消：无效的 Token'))
  }

  handleAuthorization(requestParams)
  handleHeader(requestParams)

  // 响应拦截
  return (
    chain
      .proceed(requestParams)
      .then((res) => {
        console.log('-----res', res)

        if (api?.loadingFullScreen || api?.loading) {
            loadingStore.close(loadingUUID)
        }
        const { statusCode, data } = res
        
        switch (statusCode) {
          case 200:
            return data
          default:
            return Promise.reject(res)
        }
      })
      // 请求失败拦截
      .catch((err) => {
        console.log('error requestParams-------', err)

        if (api?.loadingFullScreen || api?.loading) {
          loadingStore.close(loadingUUID)
        }
        const { statusCode, data } = err
        switch (statusCode) {
          case 401:
            userStore.setUserInfo({
              token: '',
              mobile: '',
              openId: '',
              userName: '',
              salesBrand:"",
              logoUrl:""

            })
            localStorage?.clear()
            materialStore.setFirstOpen(false)
            materialRepayStore.setFirstOpen(false)
            loadingStore.closeInitLoading()
            console.log('------11',);
            
            toastStore.show(data?.message || '请求失败')
            const routes = Taro.getCurrentPages()
            const currentRoute = routes[routes.length - 1].route ?? ''
            if (!['pages/home/<USER>', 'pages/material/index'].includes(currentRoute)) {
              Taro.reLaunch({ url: '/pages/home/<USER>' })
            }
            

          // if (requestParamsBackup.resendTime > maxResendTime) {

          //   //关闭页面手动初始loading
          //   loadingStore.closeInitLoading()
          //   requestParamsBackup.showErrorToast !== false &&
          //     toastStore.show(data?.message || '请求失败')
          //   console.log('401 requestParamsBackup', requestParams)
          //   return Promise.reject(data)
          // } else {
          //   // @ts-ignore
          //   return userStore.getToken('request 401').then((userInfo: Partial<UserInfo>) => {
          //     console.log('request 401 userInfo', userInfo)
          //     handleAuthorization(requestParamsBackup)
          //     return Taro.request(requestParamsBackup)
          //   })
          // }
          default:
            //关闭页面手动初始loading
            loadingStore.closeInitLoading()
            requestParams.showErrorToast !== false && toastStore.show(data?.msg || '请求失败')
            return Promise.reject(data)
        }
      })
  )
}
