import { loadingStore } from '@/mobx'
import Taro from '@tarojs/taro'

export type AuthorizationType = 'cms' | undefined
interface obj {
  type?: string
  url: string | object
  method: 'POST' | 'GET' | 'PUT' | 'DELETE'
  Authorization: boolean
  AuthorizationType?: AuthorizationType
  header?: any
  loading: boolean
  loadingFullScreen?: boolean
  contentType?: string
  showErrorToast?: boolean
}

interface creatorOptions {
  //是否loading
  loading: boolean
  //是否全屏loading
  loadingFullScreen: boolean
  //用于生成url的参数
  params: any
}

function creator<D = any, R = any>(apiObj: obj) {
  let loadingId = ''
  const request = (data?: D, options?: Partial<creatorOptions>, postData?: any) => {
     if (options?.loading) loadingId = loadingStore.open()
    const { params, ...restOptions } = Object.assign({}, options)
    const config: Taro.request.Option<R> = {
      ...apiObj,
      ...restOptions,
      url:
        typeof apiObj.url === 'function'
          ? apiObj.url(params || {})
          : `${apiObj.url}${postData || ''}`,
      data: data || {},
      header: {
        'content-type': apiObj.contentType || 'application/json',
        ...apiObj.header,
      },
      method: apiObj.method,
    }
    return Taro.request(config)
  }

  return request
}

function mockCreator(apiObj: obj) {
  const { header = {}, ...restOptions } = apiObj
  return creator({ header, ...restOptions })
}

export default creator
export { mockCreator }
