import { baseURL } from '../baseURL'
import creator from '../creator'

const common = {
  default: {
    get: creator({
      url: `${baseURL}/web/mobile/default.php`,
      method: 'GET',
      Authorization: true,
      loading: true,
      contentType: 'application/x-www-form-urlencoded',
    }),
    post: creator({
      url: `${baseURL}/web/mobile/default.php`,
      method: 'POST',
      Authorization: true,
      loading: true,
      contentType: 'application/x-www-form-urlencoded',
    }),
  },
}
export default common
