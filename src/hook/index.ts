// export { default as useClickScrollToCenter } from './useClickScrollToCenter'
export { default as useIsCurrentPage } from './useIsCurrentPage'
//已注册用户
export { default as useRefreshOnShowWhenChangeInBackground } from './useRefreshOnShowWhenChangeInBackground'
//静默登录
export { useSlientAuth, useSlientAuthDidShow, useSlientAuthEffect, useUnLoginRedirect } from './useSlientAuth'
export { default as useStateWithPromise } from './useStateWithPromise'
export { default as useStores } from './useStores'
export { default as useTaroSid } from './useTaroSid'
export { default as useUpdateEffect } from './useUpdateEffect'
export { default as useUUIDSelect } from './useUUIDSelect'
export { useFixedHeight, useWatchFixed } from './useWatchFixed'

