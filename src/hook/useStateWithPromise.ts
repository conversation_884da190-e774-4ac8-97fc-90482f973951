import { useEffect, useState } from 'react'

const useStateWithPromise = <T>(defaultVal: T) => {
  const [state, setState] = useState<{
    value: T
    resolve: (value: T) => void
  }>({
    value: defaultVal,
    resolve: () => {},
  })

  useEffect(() => {
    state.resolve(state.value)
  }, [state])

  return [
    state.value,
    (updater: T | ((prevValue: T) => T)) =>
      new Promise<void>((resolve) => {
        setState(((prevState) => {
          const nextVal =
            typeof updater === 'function'
              ? (updater as (prevValue: T) => T)(prevState.value)
              : updater
          return {
            value: nextVal,
            resolve,
          }
        }) as any)
      }) as Promise<void>,
  ] as const
}

export default useStateWithPromise
