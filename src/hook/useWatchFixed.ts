import { nextTick, useDidShow } from '@tarojs/taro'

import { repeatQuery, queryDom } from '@/utils'

import { commonStore } from '@/mobx'
import { FixedHeight, FixedPlaceholderHeightObj } from '@/mobx/store/common'
import { throttle } from 'lodash'
import { useCallback, useEffect, useState } from 'react'
import useIsCurrentPage from './useIsCurrentPage'
import useStores from './useStores'

//监听header tabbar等全局高度
export const useFixedHeight = () => {
  const [fixedPlaceholderHeight, setFixedPlaceholderHeight] = useState<FixedPlaceholderHeightObj>({
    top: 0,
    bottom: 0,
    topLevel: [0],
    bottomLevel: [0],
  })
  const { isCurrentPage } = useIsCurrentPage()
  useEffect(() => {
		
    if (isCurrentPage()) {
      setFixedPlaceholderHeight(commonStore.fixedPlaceholderHeight)
    }
  }, [commonStore.fixedPlaceholderHeight])

  return { fixedPlaceholderHeight }
}

export const useWatchFixed = () => {
  const { commonStore } = useStores()
  const { fixedPlaceholderHeight } = useFixedHeight()
  const [fixInited, setFixInited] = useState<boolean>(false)
  const { isCurrentPage } = useIsCurrentPage()

  const _calc = (forceUpdate = false) => {
    nextTick(async () => {
      //统一管理头底fixed部分计算
      let ret: FixedHeight = {
        header: 0,
        tabbar: 0,
        headerL2: 0,
        tabbarL2: 0,
      }
      let res: any = await queryDom('.fixed_top_1')
      ret.header = res?.height || 0
      res = await queryDom('.fixed_bottom1')
      ret.tabbar = res?.height || 0
      res = await queryDom('.fixed_bottom_2')
      ret.tabbarL2 = res?.height || 0
      if (forceUpdate) {
        commonStore.fixedHeight = ret
      } else {
        Object.assign(commonStore.fixedHeight, ret)
      }
      setFixInited(true)
    })
  }

  const repeatCalc = useCallback(
    throttle(() => {
      if (isCurrentPage()) {
        _calc(false)
      }
    }, 100),
    [],
  )
  //在后台不更新 在进入前台时强制更新一次dom
  useDidShow(() => {
    _calc(true)
    repeatQuery(repeatCalc)
  })

  useEffect(() => {
    repeatCalc()
  })

  return { fixedPlaceholderHeight, fixInited, forceUpdate: () => _calc(true) }
}
