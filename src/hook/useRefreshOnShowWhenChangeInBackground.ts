//后台监听 有变化时 等到onDidShow刷新

import { useDidHide, useDidShow } from '@tarojs/taro'
import { useEffect, useRef } from 'react'

//否则page获取的是current会导致intersection监听失败
const useRefreshOnShowWhenChangeInBackground = (
  props: {
    onRefresh: () => any
  },
  deps: Array<any> = [],
) => {
  const { onRefresh } = { ...props }
  const isChanged = useRef(false)
  const isBackground = useRef(false)
  useEffect(() => {
    if (isBackground.current) {
      isChanged.current = true
    }
  }, deps)

  useDidShow(() => {
    isBackground.current = false
    if (isChanged.current) {
      isChanged.current = false
      onRefresh?.()
    }
  })

  useDidHide(() => {
    isBackground.current = true
  })
}
export default useRefreshOnShowWhenChangeInBackground
