import { isEqual } from 'lodash'
import { useEffect, useRef } from 'react'

interface UseUpdateEffectParam {
  fireOnFirst?: boolean //初始化是否执行
  getUpdatedIndex?: boolean //获取变更的index
  isDeep?: boolean // 深比较
}

const useUpdateEffect = (
  fn: (result: { changedIndexList?: Array<number> }) => void,
  deps: React.DependencyList | undefined,
  options: UseUpdateEffectParam = {},
) => {
  const { fireOnFirst = false, getUpdatedIndex = false, isDeep = false } = options
  const didMountRef = useRef(false)
  const prevState = useRef<React.DependencyList | null>(null)

  useEffect(() => {
    // 执行回调函数
    const executeFunction = () => {
      if (!getUpdatedIndex) {
        // 如果不需要获取更新的索引，直接调用回调函数
        return fn?.({})
      }

      const changedIndexList: number[] = []
      if (deps?.length && prevState.current?.length) {
        // 遍历依赖项数组，找到更新的索引
        for (let i = 0; i < deps.length; i++) {
          const isEqualValue = isDeep
            ? isEqual(deps[i], prevState.current[i])
            : deps[i] === prevState.current[i]
          if (!isEqualValue) {
            changedIndexList.push(i)
          }
        }
      }

      // 调用回调函数，并传递更新的索引
      fn?.({ changedIndexList })
    }

    // 判断是否第一次执行
    if (fireOnFirst || didMountRef.current) {
      executeFunction()
    } else {
      // 第一次不执行，标记为已执行
      didMountRef.current = true
    }

    // 如果需要获取更新的索引，保存当前依赖项数组
    if (getUpdatedIndex) {
      prevState.current = deps || null
    }
  }, deps)
}

export default useUpdateEffect
