//静默登录

import { useStores } from '@/hook'
import { toJump } from '@/utils'
import Taro from '@tarojs/taro'
import { useDidHide, useDidShow } from '@tarojs/taro'
import { useEffect, useRef, useState } from 'react'

//登录前不展示组件
interface UseSlientAuthProps {
  //false代表允许游客登录
  needLogin?: boolean
}
export const useSlientAuth = (props: UseSlientAuthProps) => {
  const { needLogin = true } = props
  const { userStore, loadingStore } = useStores()
  const [isAuthed, setIsAuthed] = useState<boolean>(!needLogin || userStore.isInitAuthed)
  useEffect(() => {
    // if (!isAuthed) {
    //   const UUID = loadingStore.open()
    //   userStore
    //     .silentAuth('全局进入小程序首次调静默登录')
    //     .then(() => {
    //       setIsAuthed(true)
    //     })
    //     .finally(() => {
    //       loadingStore.close(UUID)
    //     })
    // }
  }, [])
  return { isAuthed }
}

//保证在静默登录完成后调用
export const useSlientAuthEffect = (fn: any) => {
  const { userStore } = useStores()

  useEffect(() => {
    if (userStore.isInitAuthed) {
      fn()
    }
  }, [userStore.isInitAuthed])
}

//保证在静默登录完成后调用
export const useSlientAuthDidShow = (fn) => {
  const { userStore } = useStores()
  const hasInitializedRef = useRef(false)

  const executeLogic = () => {
    if (!hasInitializedRef.current && userStore.isInitAuthed) {
      hasInitializedRef.current = true
      fn()
    }
  }

  useDidShow(() => {
    executeLogic()
  })

  useDidHide(() => {
    hasInitializedRef.current = false
  })

  useEffect(() => {
    executeLogic()
  }, [userStore.isInitAuthed])
}

// 未登录跳转user
export const useUnLoginRedirect = (url = '/pages/user/index') => {
  const { userStore } = useStores()
  useSlientAuthEffect(() => {
    if (!userStore.isLogin) {
      toJump(url)
    }
  });
};
