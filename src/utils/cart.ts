  import _ from 'lodash'
 
 // 更改购物车 商品数量

  export const onUpdateGoodSl = (info,list) => {
    let beforeV = _.cloneDeep(list)
    const res = beforeV.filter((item) => info.barcode == item?.barcode)
    res[0].sl = info.sl
    return beforeV
  }

 export const onDeleteGoods = (value: string[],list) => {
    let beforeV = _.cloneDeep(list)
    const res = beforeV.filter((item) => !value.includes(item.barcode))
    return res
  }

 export const onAddCart = (info,list) => {

    let beforeV = _.cloneDeep(list)
    const res = beforeV.filter((item) => info.barcode == item?.barcode)
    if (res.length) {
      res[0].sl = res[0].sl + info.sl
    } else {
      beforeV = [info, ...beforeV]
    }
    return beforeV

  }

  export default {

    onUpdateGoodSl,
    onDeleteGoods,
    onAddCart
  }


  




