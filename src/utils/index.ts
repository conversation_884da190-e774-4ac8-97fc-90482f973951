import Taro, { scanCode, createSelectorQuery } from '@tarojs/taro'
import numeral from 'numeral'

import { tabbarList } from '@/constants/config'

import projectConfig from '../../project.config.json'

export const systemInfo: any = Taro.getSystemInfoSync()
export const menuButtonReact: any = Taro.getMenuButtonBoundingClientRect()
export const accountInfo: any = Taro.getAccountInfoSync()

export const getAppID = () => projectConfig.appid

//链接跳转/锚点跳转
export function toJump(_url = '', redirectTo = false) {
  try {
    console.log('_url', _url)

    if (!_url || _url === '/') return
    const jumpLink = _url.split('||')
    if (jumpLink[1] && jumpLink[0]?.split('wxappid=')) {
      Taro.navigateToMiniProgram({
        path: '/' + jumpLink[1],
        appId: jumpLink[0]?.split('wxappid=')[1],
        envVersion: 'release',
      })
      return
    }
    const url = _url.indexOf('#') >= 0 ? _url : _url
    if (_url.includes('https')) {
      Taro.navigateTo({
        url: `/subpackages/webView/index?webUrl=${encodeURIComponent(_url)}`,
      })
      return
    }
    //tabbar页面
    const arr = tabbarList
    if (arr.indexOf(url) !== -1 || arr.indexOf('/' + url?.split('?')[0]) !== -1) {
      Taro.switchTab({
        url: url.slice(0, 1) == '/' ? url : '/' + url,
      })
    } else if (url && url.includes('#')) {
      const str = url.split('#')[1]
      if (str) {
        Taro.pageScrollTo({
          selector: `#${str}`,
          duration: 500,
          offsetTop: -130,
        })
      }
    } else {
      let link = ''
      if (url) {
        link = url.replace(/{store_view}/g, 'wx-mp')
      }
      let _url = link.slice(0, 1) == '/' ? link : '/' + link
      if (Taro.getCurrentPages().length > 8) {
        Taro.reLaunch({ url: _url })
      } else if (redirectTo) {
        Taro.redirectTo({ url: _url }).catch(() => {}) //防止sentry报错
      } else {
        Taro.navigateTo({ url: _url }).catch(() => {}) //防止sentry报错
      }
    }
  } catch (error) {
    console.log('Error in toJump', error)
  }
}

export const generateUUID = () => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    const r = (Math.random() * 16) | 0
    const v = c === 'x' ? r : (r & 0x3) | 0x8
    return v.toString(16)
  })
}

const delayPromise = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms))
//将异步/同步函数改为异步
const wrapAsyncFunction = async (callback: () => any) => {
  try {
    const result = await Promise.resolve(callback())
    return result
  } catch (error) {
    return Promise.reject(error)
  }
}

//重复执行 默认间隔100,200,200ms
export const repeatQuery = async (callback: () => any, delays: Array<number> = [100, 300, 500]) => {
  for (const delay of delays) {
    await delayPromise(delay)
    await wrapAsyncFunction(callback)
  }
}
export const queryDom = async (param) => {
  return new Promise((resolve) => {
    createSelectorQuery()
      .select(param)
      ?.boundingClientRect()
      .exec((res) => {
        resolve(res && res[0])
      })
  })
}

/**
 * 微信扫码
 *
 */

export const scanCodeFn = async () => {
  return scanCode({
    onlyFromCamera: true,
    success(res) {
      console.log(res)
      return res
    },
    fail() {
      console.log('扫码失败')
      return ''
    },
  })
}

// 获取状态栏高度
export const getNavBarInfo = () => {
  const rect = Taro.getMenuButtonBoundingClientRect()
  const navBarHeight = rect.bottom - rect.top + (rect.top - systemInfo.statusBarHeight) * 2
  const navBarAndStatusBarHeight = navBarHeight + systemInfo.statusBarHeight
  let bottomBarHeight = systemInfo.screenHeight - systemInfo.windowHeight - systemInfo.statusBarHeight;

  if(bottomBarHeight<=0 )  bottomBarHeight = 34
  const environment = Taro.getDeviceInfo()

  
  
  return {
    navBarHeight: navBarHeight,
    navBarAndStatusBarHeight: navBarAndStatusBarHeight,
    bottomBarHeight:bottomBarHeight,
    ios:environment.system.includes('iOS')
    
  }
}

// 自定义格式化函数
export function formatNumber(num) {
  if (num == '-' || num === null) return '--'
  let formattedNum = ''
  if (num >= 100000000) {
    // 超过1亿
    formattedNum = numeral(num / 100000000).format('0,0.00') + '亿'
  } else if (num >= 10000) {
    // 超过1万
    formattedNum = numeral(num / 10000).format('0,0.00') + 'w'
  } else {
    // 不超过1万，保留两位小数并添加千分位逗号分隔
    formattedNum = numeral(num).format('0,0.00')
  }
  return formattedNum
}

export function formatToTwoDecimals(num: number): string {
  // 先四舍五入到两位小数，再格式化为字符串
  const rounded = Math.round(num * 100) / 100
  return rounded.toFixed(2)
}

// 订阅相关通知
export const onSubscribeMessage = (successF:()=> void,completeF:()=>void) => {
  Taro.requestSubscribeMessage({
    tmplIds: [
      'zy3UQDSTIkHi3eNijjZI0eH2dQEPuUVb0u2MVmO3iUg',
      'MB6Rxa8Z1GQK_FaCqOcDmC8nex8asx17e_KEDQ-t_7Q',
      'KU-W1utjKecoiDjbkB6rr-6sBMHcVwzkXOdlBfDBdHQ',
      // '-jTHpg2zKcN2AqkERjZNOKwUjdL3v9fCGm4BJgHoaIM'
    ],
    success: async (res) => {
      console.log('-------success--订阅');
      
      successF?.()
    },
    fail: (res) => {
      console.log('-------fail',res);

    },
    complete: () => {
      completeF?.()
    },
  })
}
