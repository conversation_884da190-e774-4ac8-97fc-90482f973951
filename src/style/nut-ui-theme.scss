$color-primary: #2f2f30;

.nut-popup {
  .nut-popup-title-right {
    top: 23px;
    right: 18px;

    .nut-icon-Close {
      width: 22px;
      height: 22px;
      color: #212322;
    }
  }
}

.nut-dialog-outer.pos_theme {
  --nutui-dialog-padding: 32px;
  --nutui-dialog-width: 640px;
  --nutui-dialog-border-radius: 8px;
  --nutui-dialog-close-color: $color-primary;
  --nutui-dialog-close-top: 32px;
  --nutui-dialog-close-right: 28px;
  --nutui-dialog-header-font-size: 22px;
  --nutui-dialog-content-margin: 0;

  .nut-dialog-close {
    height: 24px;
    width: 24px;
  }

  .nut-dialog {
    padding: 32px 0 0 0;

    .nut-dialog-header {
      height: 32px;
      line-height: 32px;
      font-weight: 500;
      font-size: 24px;
      color: #212322;

    }

    .nut-dialog-content {
      width: 100%;
      padding: 40px 24px;
      box-sizing: border-box;
      color: #2F2F30;
      font-size: 20px;
      line-height: 24px;
    }

    .nut-dialog-footer {
      justify-content: space-between;
      padding: 16px 32px 32px;
      box-sizing: border-box;

      .nut-button {
        width: calc(50% - 12px);
        height: 64px;
        border-radius: 64px;
        font-size: 18px;

        &.nut-dialog-footer-cancel.nut-dialog-footer-cancel {
          margin-right: 0;
          border: 1.5px solid var(--, #2F2F30)
        }

        &.nut-button-primary {
          background: $color-primary;
          color: #ffffff;

          &.button-hover {
            color: #ffffff;
          }
        }
      }
    }
  }
}

.materialDataPicker {
  .nut-picker-cancel-btn {
      color: #707070;
  }
  .nut-picker-cancel-btn,.nut-picker-title,.nut-picker-confirm-btn {
    font-size: 14px;
    font-weight: 400;
  }
  .nut-picker-confirm-btn {
    font-weight: 500;
    color: #F39800;

  }
  .nut-picker-roller-item-title {
    font-size: 12px;
  }



}
