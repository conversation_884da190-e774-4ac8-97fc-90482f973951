//var extend mixin已经在全局config配置了不需要再引入
// @import "./var";
//- 使用 % 定义一个样式占位符（Placeholder），它不会生成实际的 CSS 输出。
// @import "./extend";
// @import "./mixin";

// @import "./iconfont";
// @import './flex';
// @import './reset';
// @import './taroify';
@import './nut-ui-theme';

page {
  // - readme zindex管理
  --popup-z-index: 10001;
  --backdrop-z-index: 10000;
  --nutui-popup-border-radius: 0; // 虽然隐藏但是需要在dom里避免切换闪烁优化体验
  --nutui-dialog-close-width: 24px;
  --nutui-dialog-close-color: #000;
  --nutui-dialog-header-font-size: 24px;
  --nutui-font-size-3: 20px;
  --nutui-dialog-content-text-align: center;
  --nutui-dialog-content-margin: 40px auto;
  --nutui-gray-6: #000;
  --nutui-toast-inner-padding: 5px;
  --nutui-toast-text-font-size: 14px !important;

  ::-webkit-scrollbar {
    display: none;
    width: 0;
    height: 0;
    color: transparent;
  }

  // 使用于内部有image的 不然displaynone内部的image高度会变成0
  .height_hidden {
    overflow: hidden !important;
    height: 0 !important;
    padding: 0 !important;
    margin: 0 !important;
    border: 0 !important;
    //否则无法阻止内部的fixed元素
    visibility: hidden !important;
  }

  .display_none {
    display: none !important;
  }

  .text_center {
    text-align: center;
  }

  .oneClickLogin {
    width: 136px;
    height: 40px;
    color: #fff;
    background-color: #f39800;
    font-size: 14px;
    display: flex;
    justify-content: center;
    align-items: center;
    border: none;
    outline: none;
    &:after {
      border: 1px solid transparent
    }
  }

  .singleLineOmit {
    width: 100%;
    max-width: 100%;
    white-space: nowrap;
    /* 防止文本换行 */
    overflow: hidden;
    /* 隐藏溢出的内容 */
    text-overflow: ellipsis;
    /* 显示省略符号来代表被修剪的文本 */

  }
}
