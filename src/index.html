<!DOCTYPE html>
<html>

<head>
  <meta content="text/html; charset=utf-8" http-equiv="Content-Type">
  <meta content="width=device-width,initial-scale=1,user-scalable=no" name="viewport">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-touch-fullscreen" content="yes">
  <meta name="format-detection" content="telephone=no,address=no">
  <meta name="apple-mobile-web-app-status-bar-style" content="white">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <title>pos_mini</title>
  <script>
    < %= htmlWebpackPlugin.options.script % >

  </script>
</head>

<body>
  <div id="app"></div>
  <script>
    // main.js
    const setRootFontSize = () => {
      let screenWidth = document.documentElement.clientWidth || document.body.clientWidth;
      let rootFontSize = screenWidth / 10;
      document.documentElement.style.fontSize = rootFontSize + 'px';
    };

    window.addEventListener('load', setRootFontSize);
    window.addEventListener('resize', setRootFontSize);

  </script>
</body>

</html>
