.page_cash_desk {
  background: #f0f0f0;
  --bottom-height: 108px;

  .page_cash_desk_layout {
    height: calc(100vh - var(--headerbar-height) - var(--bottom-height));
    padding: var(--padding-top) 0 0;
    width: 100%;
    box-sizing: border-box;


    .dealLine {
      height: 112px;
      margin-bottom: 110px;
      position: relative;
      background-color: #2F2F30;
      
      .dealLine_content {
        background-color: #fff;
        width: calc(100vw - 48px);
        height: 170px;
        padding: 40px 0;
        border-radius: 8px;
        box-sizing: border-box;
        position: absolute;
        top: 32px;
        left: 24px;
        .tip {
          color: #2F2F30;
          font-size: 32px;
          font-weight: 38px;
          font-weight: 500;
          display: flex;
          align-items: center;
          justify-content: center;
          .time {
            margin-left: 16px;
            color: #DFB872;
          }
        }
        .tip_two {
          margin-top: 20px;
          color: #818181;
          font-size: 28px;
          line-height: 32px;
          text-align: center;

        }
        

      }

     

    }
    .com_member_select {
      background: #fff;
      border: none;
      width: calc(100% - 24px * 2);
      margin: 0 auto;
    }

    .cash_desk_item {
      background: #fff;
      box-sizing: border-box;
      border-radius: 8px;
      padding: 22px;
      width: calc(100% - 24px * 2);
      margin: 18px auto 0;

      .cash_desk_item_title {
        height: 52px;
        font-size: 22px;
        line-height: 28px;
        font-weight: 500;
        border-bottom: 1px solid #f2f2f2;
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
      }

      &.pay_way {
        --nutui-checkbox-label-font-size: 22px;

        .nut-checkbox-label {
          font-weight: 500;
        }
      }

      .cash_desk_item_content {
        .label_value_item {
          margin-top: 22px;
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          align-items: center;
          height: 26px;
          font-size: 20px;

          .label_value_item_label {
            color: #818181;
          }

          .label_value_item_value {
            color: #2f2f30;
          }

          .input_item {
            display: flex;
            align-items: center;
            input {
              border: 1px solid #c6c6c6;
              border-radius: 4px;
              padding: 12px 14px;
              font-size: 20px;
              width: 142px;
              line-height: 48px;
            }
          }
          .input_item:before {
            content: '-¥';
            margin-right: 4px;
          }
        }
      }

      &.discount_details {
        .discount_details_bottom {
          display: flex;
          flex-direction: row;
          align-items: center;
          justify-content: space-between;
          padding-top: 24px;
          margin-top: 24px;
          border-top: 1px solid #f3f3f3;

          .discount_btn {
            width: 120px;
            height: 48px;
            border-radius: 48px;
            font-size: 20px;
            border: 1px solid #818181;
          }

          .discount_desc {
            font-size: 22px;
            font-weight: 500;
            color: #2f2f30;
            display: flex;
            height: 48px;
            line-height: 48px;

            .unit {
              font-size: 18px;
              margin-left: 12px;
              margin-right: 2px;
              line-height: 52px;
            }
          }
        }
      }

      //支付方式
      &.pay_way {
        .cash_desk_item_title {
          border-bottom: none;
        }

        .pay_way_item_group {
          display: flex;
          flex-direction: row;
          flex-wrap: wrap;
          gap: 24px;

          .pay_way_item {
            position: relative;
            width: calc((100% - 24px * 2) / 3);
            border-radius: 4px;
            border: 1px solid #f0f0f0;
            display: flex;
            flex-direction: row;
            height: 90px;
            align-items: center;
            padding-left: 16px;
            box-sizing: border-box;

            .pay_way_item_checked_icon {
              position: absolute;
              top: -1px;
              right: -1px;
              visibility: hidden;
            }

            &.isChecked {
              border: 1px solid #2f2f30;

              .pay_way_item_checked_icon {
                visibility: visible;
              }
            }

            .pay_way_item_label {
              padding-left: 16px;
              font-size: 20px;
            }
          }
        }
      }
    }
  }

  @keyframes showPopup {
    from {
      transform: translateY(100%);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }
  @keyframes hidePopup {
    from {
      transform: translateY(0);
      opacity: 1;
    }
    to {
      transform: translateY(100%);
      opacity: 0;
    }
  }

  .page_cash_desk_popup_layout {
    position: fixed;
    bottom: 0;
    height: 100vh;
    width: 100%;
    pointer-events: none;

    .page_cash_desk_popup {
      position: absolute;
      bottom: var(--bottom-height);
      left: 0;
      width: 100%;
      height: calc(100% - var(--bottom-height));
      pointer-events: all;
      flex-direction: column;
      display: flex;
      opacity: 0;
      visibility: hidden;
      transition: opacity 0.3s ease, visibility 0.3s ease;

      &.show {
        opacity: 100;
        visibility: visible;
      }

      .mask {
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.2);
        flex: 1;
      }

      .popup_content {
        position: absolute;
        bottom: 0;
        left: 0;
        transition: dispaly 0.3s ease;
        min-height: 236px;
        background: #ffffff;
        padding: 32px 24px;
        width: calc(100% - 24px * 2);

        &.open {
          animation: showPopup 0.2s ease-out forwards;
        }

        &.close {
          animation: hidePopup 0.2s ease-out forwards;
        }

        .popup_title_layout {
          display: flex;
          justify-content: space-between;
          align-items: center;
          height: 32px;
          font-size: 24px;
          color: #212322;
          font-weight: 500;
        }

        .popup_desc {
          margin-top: 32px;
          height: 48px;
          line-height: 24px;
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          border-bottom: 1px solid #f4f4f4;
          font-size: 20px;
          font-weight: 500;
        }

        .popup_pay_item {
          display: flex;
          flex-direction: row;
          align-items: center;
          justify-content: space-between;
          height: 28px;
          font-size: 20px;
          line-height: 28px;
          padding-bottom: 12px;
          margin-top: 24px;

          .popup_pay_item_value_layout {
            display: flex;
            flex-direction: row;
            align-items: center;

            .popup_pay_item_value {
              display: flex;
              flex-direction: row;
              height: 28px;
              padding-right: 16px;

              .unit {
                font-size: 16px;
                line-height: 32px;
              }
            }

            .popup_pay_item_delete_layout {
              height: 28px;
              display: flex;
              align-items: center;

              .popup_pay_item_delete_icon {
                position: relative;
                width: 24px;
                padding-left: 16px;
                height: 24px;
                display: flex;
                align-items: center;
                justify-content: center;

                &.hide {
                  display: none;
                }

                &:before {
                  content: '';
                  position: absolute;
                  top: 6px;
                  left: 0;
                  width: 1px;
                  height: 12px;
                  background: #dddddd;
                }
              }
            }
          }
        }
      }
    }
  }

  .page_cash_desk_bottom {
    position: fixed;
    bottom: 0;
    left: 0;
    padding: 0 18px;
    width: calc(100% - 18px * 2);
    height: var(--bottom-height);
    z-index: 10;
    background: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: rgba(0, 0, 0, 0.15) 0 -2px 16px 0;
    pointer-events: all;

    .page_cash_desk_bottom_left {
      display: flex;
      flex-direction: column;
      flex: 1;
      height: 64px;
      justify-content: space-between;

      .page_cash_desk_bottom_left_top {
        display: flex;
        flex-direction: row;
        font-size: 20px;
        height: 28px;
        align-items: center;

        .separator {
          font-size: 16px;
          line-height: 16px;
          padding: 0 6px;
        }

        .num {
          font-size: 24px;
        }
      }

      .page_cash_desk_bottom_left_bottom {
        height: 28px;
        line-height: 28px;
        color: #818181;
        font-size: 20px;
        display: flex;
        flex-direction: row;
        align-items: center;

        .page_cash_desk_bottom_left_bottom_btn {
          width: 88px;
          height: 28px;
          text-align: center;
          font-size: 16px;
          color: #818181;
          border-radius: 28px;
          border: 1px solid #2f2f30;
          box-sizing: border-box;
          margin-left: 8px;
        }
      }
    }

    .confirm_btn {
      width: 208px;
      height: 64px;
      line-height: 64px;
      border-radius: 64px;
      font-size: 20px;
      background: #2f2f30;
      text-align: center;
      color: #ffffff;

      &.nut-button-disabled {
        background: #818181 !important;
      }
    }
  }
}
