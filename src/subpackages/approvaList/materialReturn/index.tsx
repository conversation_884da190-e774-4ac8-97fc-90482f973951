import { View } from '@tarojs/components'
import { MainLayout } from '@/components'
import { ArrowDown, Add } from '@nutui/icons-react-taro'
import { SafeArea } from '@nutui/nutui-react-taro'

import BaseInfo from '@/subpackages/approvaList/applyForDetail/BaseInfo'
import CanEditGood from '@/subpackages/approvaList/components/CanEditGood'
import TitleRow from '@/subpackages/approvaList/components/TitleRow'
import IconFont from '@/components/iconfont'

import { getNavBarInfo } from '@/utils'

import './index.scss'

const MaterialReturn = () => {
  const { navBarAndStatusBarHeight: barHeight } = getNavBarInfo()

  return (
    <MainLayout
      className='materialApply'
      initOptions={{
        inited: true,
        initLoading: true,
      }}
      headBarConfig={{
        showBack: true,
        showSearch: false,
        backgroundColor: '#fff',
        color: '#000',
        headerTitle: '物料归还明细',
        icon: '',
      }}
      style={{ backgroundColor: '#F7F8FA' }}
      showHeaderBar
      showTabBar={false}
    >
      <View className='content' style={{ height: `calc(100vh - ${barHeight}px)` }}>
        <BaseInfo />

        <View className='materialTitle'>
          <TitleRow
            leftV='物料明细'
            rightV={
              <View className='addR'>
                <IconFont name='a-bianji11' color='#F39800' size={10} />
                添加物料
              </View>
            }
          ></TitleRow>
        </View>
        <View className='materialContent'>
          <CanEditGood />
          <CanEditGood />
          <CanEditGood />
        </View>

        <View className='btns'>
          <View className='cancel btn'>终止</View>
          <View className='sure btn'>提交审批</View>
        </View>
      </View>
    </MainLayout>
  )
}

export default MaterialReturn
