.materialApply {
  overflow: scroll;
  height: 100%;

  .content {

    display: flex;
    flex-direction: column;
    overflow: scroll;
    padding-bottom: calc(env(safe-area-inset-bottom) * var(--nutui-safe-area-multiple, 1) + 50px);
    box-sizing: border-box;

    .addR {
      display: flex;
      align-items: center;
      font-size: 12px;
      line-height: 16px;
      color: #f39800;
    }

    .materialTitle {
      padding: 12px 24px;
      margin-top: 8px;
      background-color: #fff;

    }

    .materialContent {
      background-color: #fff;


    }

    .btns {
      position: absolute;
      left: 0;
      bottom: 0;
      background-color: #fff;

      width: 100%;
      padding: 9px 24px 0;
      padding-bottom: calc(env(safe-area-inset-bottom) * var(--nutui-safe-area-multiple, 1));
      display: flex;
      justify-content: space-between;
      align-items: center;
      box-sizing: border-box;
      box-shadow: 0px 0px 8px 0px #0000000A;


      .btn {
        flex: 1;
        height: 40px;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 14px;
      }

      .cancel {
        margin-right: 12px;
        border: 1px solid #BEBEBE;
        color: #111111;
        background-color: #fff;
      }

      .sure {
        color: #fff;
        background-color: #F39800;
      }


    }



  }


}
