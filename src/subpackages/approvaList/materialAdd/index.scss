.materialRepay {
  text-align: center;

  .content {
    display: flex;
    flex-direction: column;
    background-color: #fff;
    .topArea {
      padding: 0 16px;
      margin-top: 12px;
    }

    .categoryBox {
      flex: 1;
      margin-top: 12px;
      overflow: scroll;
      display: flex;
      position: relative;
      .left {
        width: 100px;
        height: 100%;
        box-sizing: border-box;
        background-color: #fafafa;
      }
      .right {
        flex: 1;
        height: 100%;
        box-sizing: border-box;
      }
    }
    .noRepay {
      flex: 1;
      overflow: hidden;
      box-sizing: border-box;
      padding-top: 185px;
      display: flex;
      justify-content: center;
    }
  }
}
