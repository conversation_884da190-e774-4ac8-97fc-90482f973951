import { View } from '@tarojs/components'
import MainLayout from '@/components/MainLayout'
import { getNavBarInfo } from '@/utils'
import { SafeArea } from '@nutui/nutui-react-taro'
import { observer } from 'mobx-react'
import { useState, useEffect } from 'react'

import Taro, { useRouter } from '@tarojs/taro'
import SearchBar from '@/components/SearchBar'
import SideMenu from '@/components/SideMenu'
import ProductList from '@/components/ProductList'
import CartInfo from '@/components/CartInfo'
import MaterialInfo from '@/components/MaterialInfo'
import { useStores } from '@/hook'
import http from '@/http'
import NoOrder from '@/components/NoOrder'
import cartUtil from '@/utils/cart'

import './index.scss'

const MaterialAdd = () => {
  const { navBarAndStatusBarHeight: barHeight,bottomBarHeight } = getNavBarInfo()
  const { materialAddStore, userStore } = useStores()
  const { menus, selectedMenuValue, selectedGoodsList, checkedGoodsList } = materialAddStore

  const [materialInfoVisible, setMaterialInfoVisible] = useState(false)
  const [currentGood, setCurrentGood] = useState({})
  const { type, id } = useRouter().params

  useEffect(() => {
    console.log('----type', type)
    const res = type == 3 ? 2 : type
    materialAddStore.setType(res)
    materialAddStore.getMenus(res)
  }, [type])

  const onClickSide = (item) => {
    materialAddStore.setSelectedMenuValue(item.category_code)
  }

  const getGoodInfo = async (item) => {
    const res = await http.common.default.post(
      {
        barcode: item.barcode,
        type: type == 0 || type == 1 ? type : 2,
        dj_id: id,
      },
      {},
      '?c=barcode&m=barcode'
    )
    if (res.code == 0) {
      setCurrentGood(res.data)
      setMaterialInfoVisible(true)
    }
  }

  // 加入购物车
  const onAddCart = (info) => {
    const selectedList = cartUtil.onAddCart(info, selectedGoodsList)
    const checkedList = cartUtil.onAddCart(info, checkedGoodsList)

    const barCodes = checkedList.map((item) => item.barcode)

    selectedList.forEach((item) => {
      const index = barCodes.findIndex((code) => code == item.barcode)
      if (index >= 0) {
        item.sl = checkedList[index].sl
      }
    })

    materialAddStore.setSelectedGoodsList(selectedList)
    materialAddStore.setCheckedGoodsList(checkedList)
    setMaterialInfoVisible(false)
  }

  // 更改购物车 商品数量
  const onUpdateGoodSl = (info) => {
    const selectedList = cartUtil.onUpdateGoodSl(info, selectedGoodsList)
    materialAddStore.setSelectedGoodsList(selectedList)
    const checkedList = cartUtil.onUpdateGoodSl(info, checkedGoodsList)
    materialAddStore.setCheckedGoodsList(checkedList)
  }

  // 删除购物车商品
  const onDeleteGoods = (value: string[]) => {
    const selectedList = cartUtil.onDeleteGoods(value, selectedGoodsList)
    materialAddStore.setSelectedGoodsList(selectedList)
    const checkedList = cartUtil.onDeleteGoods(value, checkedGoodsList)
    materialAddStore.setCheckedGoodsList(checkedList)
  }

  // 提交订单
  const onSubmit = async () => {
    if (!checkedGoodsList.length) {
      Taro.showToast({
        title: '请至少选择一个物料',
        icon: 'none',
        duration: 2000,

      })
      return
    }

    const sku = checkedGoodsList.map((item) => {
      return {
        barcode: item.barcode,
        sl: item.sl,
      }
    })
    const orderRes = await http.common.default.post(
      {
        dj_id: id,
        mx: JSON.stringify(sku),
      },
      {},
      '?c=zdwld&m=add_skus'
    )

    if (orderRes.code == 0) {
      // Taro.navigateTo({
      //   url:`/subpackages/approvaList/materialApply/index?id=${orderRes.data.id}&type=${type}`
      // })
      materialAddStore.setSelectedGoodsList([])
      materialAddStore.setCheckedGoodsList([])

      Taro.navigateBack()
    } else {
      Taro.showToast({
        title: orderRes.msg,
        icon:'none',
        duration: 2000,

      })
    }
  }

  const onCheckGoods = (value: string[]) => {
    const res = selectedGoodsList.filter((item) => value.includes(item.barcode))
    materialAddStore.setCheckedGoodsList(res)
  }
  return (
    <MainLayout
      className='materialRepay'
      initOptions={{
        inited: true,
        initLoading: true,
      }}
      headBarConfig={{
        showBack: true,
        showSearch: false,
        backgroundColor: '#fff',
        color: '#000',
        headerTitle: '物料列表',
        icon: '',
      }}
      style={{ backgroundColor: '#F7F8FA' }}
      showHeaderBar
      showTabBar={false}
    >
      {/* 76px 底部tabbar高度 */}
      <View className='content ' style={{ height: `calc(100vh - ${barHeight}px  )` }}>
        <View className='topArea'>
          <SearchBar disabled />
        </View>

        {menus.length ? (
          <View className='categoryBox'>
            {/* 左侧菜单 */}
            <View className='left'>
              <SideMenu list={menus} onChange={onClickSide} categoryV={selectedMenuValue} />
            </View>
            {/* 右侧商品 */}
            <View className='right'>
              <ProductList list={materialAddStore.goodsList} onChange={getGoodInfo} />
            </View>
            {!!selectedGoodsList.length && (
              <CartInfo
                isNeedBottomBar
                type={type}
                popupBottomPosition={0}
                list={selectedGoodsList}
                checklist={checkedGoodsList}
                onUpdateSl={onUpdateGoodSl}
                onDelete={onDeleteGoods}
                onSubmit={onSubmit}
                onCheck={onCheckGoods}
                btnText='确认添加'
              />
            )}
          </View>
        ) : (
          <View className='noRepay'>
            <NoOrder title='暂无归还的物料' />
          </View>
        )}

        <MaterialInfo
          visible={materialInfoVisible}
          typeName={type == 2 ? '归还单' : '申请单'}
          onSure={onAddCart}
          onClose={() => setMaterialInfoVisible(false)}
          info={currentGood}
          type={type}
          checklist={checkedGoodsList}
        />
        {!!selectedGoodsList.length && <SafeArea position='bottom' />}
      </View>
    </MainLayout>
  )
}

export default observer(MaterialAdd)
