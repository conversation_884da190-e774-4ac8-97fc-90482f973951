import { View } from '@tarojs/components'
import {
  Form,
  Button,
  Input,
  TextArea,
  SafeArea,
  Picker,
} from '@nutui/nutui-react-taro'
import {  useEffect, useState } from 'react'
import { MainLayout } from '@/components'
import Taro from '@tarojs/taro'
import { getNavBarInfo } from '@/utils'
import http from '@/http'
import { useStores } from '@/hook'
import { observer } from 'mobx-react'
import IconFont from '@/components/iconfont'

import './index.scss'

interface PickerOption {
  text: string | number
  value: string | number
  disabled?: boolean
  children?: PickerOption[]
  className?: string | number
}
const ModifyAddress = () => {
  const { navBarAndStatusBarHeight: barHeight } = getNavBarInfo()
  const [isVisible, setIsVisible] = useState(false)

  const { materialApplyStore,addressStore } = useStores()
  const { baseInfo } = materialApplyStore

  const [customCityData, setCustomCityData] = useState([])
  const [cityCustom, setCityCustom] = useState([])

  const [form] = Form.useForm()

  useEffect(() => {
    
    if(!addressStore?.addressList?.length){
      getList()
    }else {
      setCustomCityData([...addressStore.addressList])
    }
  }, [])

  useEffect(() => {
    const { province_id, city_id, district_id } = baseInfo

    setCityCustom([province_id, city_id, district_id])
    form.setFieldsValue({
      gkname: baseInfo.gkname,
      phone: baseInfo.phone,
      area:baseInfo.province? baseInfo.province + '-' + baseInfo.city + '-' + baseInfo.district:'',
      address: baseInfo.address,
    })
  }, [baseInfo])

  const onConfirmArea = (options: PickerOption[], values: (string | number)[]) => {
    const str = options.map((item) => item.text).join('-')
    setCityCustom(values)
    form.setFieldValue('area', str)
  }

  const submitFailed = (error: any) => {
    // Taro.showToast({ title: JSON.stringify(error), icon: 'error' })
  }

  const submitSucceed = (values: any) => {
    const res = form.getFieldsValue(true)
    const info = res.area.split('-')
    const partInfo = {
      province: info[0],
      city: info[1],
      district: info[2],
      province_id: cityCustom[0],
      city_id: cityCustom[1],
      district_id: cityCustom[2],
    }

    materialApplyStore.setBaseInfo({ ...baseInfo, ...res, ...partInfo })
    Taro.navigateBack()
  }

  const getList = async () => {
    const res = await http.common.default.get({}, {}, '?c=region&m=list')
    if (res.code == 0) {
      const transformData = (data: any[]): any[] => {
        return data.map((item) => {
          const newItem = {
            value: item.region_id,
            text: item.region_name,
          }
          if (item.children && item.children.length > 0) {
            newItem.children = transformData(item.children)
          }
          return newItem
        })
      }

      const list = transformData(res.data)
      setCustomCityData(list)
      addressStore.setAddressList(list)
      
    }
  }

  const onConfirmUpdate = async () => {
    form.submit()

  }

  return (
    <MainLayout
      className='modifyAddressPage'
      initOptions={{
        inited: true,
        initLoading: true,
      }}
      headBarConfig={{
        showBack: true,
        showSearch: false,
        backgroundColor: '#fff',
        color: '#000',
        headerTitle: '修改地址',
        icon: '',
      }}
      style={{ backgroundColor: '#F7F8FA' }}
      showHeaderBar
      showTabBar={false}
    >
      <View className='formBox' style={{ height: `calc(100vh - ${barHeight}px )` }}>
        <Form
          form={form}
          divider
          labelPosition='top'
          starPosition='right'
          onFinish={(values) => submitSucceed(values)}
          onFinishFailed={(values, errors) => submitFailed(errors)}
        >
          <Form.Item
            label='收货人姓名'
            name='gkname'
            rules={[{ required: true, message: '请输入收货人姓名' }]}
          >
            <Input placeholder='请输入收货人姓名' type='text' />
          </Form.Item>
          <Form.Item
            label='手机号码'
            name='phone'
            rules={[{ required: true, message: '请输入手机号码' }]}
          >
            <Input placeholder='请输入手机号码' type='digit' maxLength={11} />
          </Form.Item>
          <Form.Item shouldUpdate  className='areaRow'>
            {() => {
              return (
                <>
                  <View className='dropDownIcon' onClick={()=> setIsVisible(true)}>
                    <IconFont name='icon' size={14} color='#212322' />
                  </View>
                  <Form.Item
                    
                    label='选择区域'
                    name='area'
                    trigger='onConfirm'
                    getValueFromEvent={(...args) => args[1]}
                    onClick={() => {
                      setIsVisible(true)
                    }}
                    rules={[{ required: true, message: '请选择区域' }]}
                  >
                    <Input type='text' placeholder='请选择区域' disabled></Input>
                  </Form.Item>
                </>
              )
            }}
          </Form.Item>

          <Form.Item
            label='详细地址'
            name='address'
            rules={[{ required: true, message: '请输入详细地址' }]}
          >
            <TextArea rows={1} autoSize placeholder='请输入详细地址' />
          </Form.Item>
        </Form>
        <View
          style={{
            display: 'flex',
            justifyContent: 'center',
            width: '100%',
          }}
          className='footerBtns'
        >
          <View className='btn'>
            <Button type='primary' onClick={onConfirmUpdate}>
              确认修改
            </Button>
          </View>
        </View>

        <View className='pickerMy'>
          <Picker
            visible={isVisible}
            options={customCityData}
            value={cityCustom}
            onClose={() => setIsVisible(false)}
            onConfirm={(list, values) => onConfirmArea(list, values)}
            onChange={(options: PickerOption[], value: (string | number)[], columnIndex: number) =>
              console.log('多级联动', columnIndex, value, options)
            }
          />
        </View>

        <SafeArea position='bottom' />
      </View>
    </MainLayout>
  )
}

export default observer(ModifyAddress)
