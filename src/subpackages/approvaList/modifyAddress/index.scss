.modifyAddressPage {
  .formBox {
    padding-top: 16px;
    background-color: #fff;
    box-sizing: border-box;
    position: relative;

    display: flex;

    flex-direction: column;

    --nutui-form-item-required-color: #e34d59;

    .nut-cell-group-wrap {
      .nut-cell {
        padding: 0 20px 4px;
        margin-bottom: 24px;

        .nut-cell-title {
          position: relative;
          padding-right: 0;
          padding-bottom: 0;
          margin-bottom: 4px;
          margin-right: 0;
          height: 16px;
          display: flex;
          align-items: center;
          width: auto;

          .nut-form-item-labeltxt {
            font-size: 12px;
            line-height: 16px;
            color: #707070;
            // padding-left: 10px;
          }

          .nut-form-item-label-required {
            left: auto;
            right: -16px !important;
            top: 0;

            width: 10px;
            height: 16px;
            font-size: 16px;
            display: flex;
            align-items: flex-end;
          }
        }

        .nut-form-item-body {
          .nut-form-item-body-slots {
            .nut-input,
            .nut-textarea-textarea {
              color: #111111;
              font-size: 12px;
              line-height: 18px;
            }

            .nut-textarea-textarea {
            }
          }
        }
      }
    }

    .footerBtns {
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: flex-end;

      .btn {
        padding: 9px 32px 0;
        box-shadow: 0px 0px 8px 0px #0000000a;
        width: 100%;
        display: flex;
        justify-content: center;

        .nut-button {
          background: #f39800;
          font-size: 14px;
          line-height: 20px;
          color: #fff;
          width: 100%;
          height: 40px;
          border-radius: 4px;
        }
      }
    }

    .pickerMy {
      .nut-picker-cancel-btn {
        color: #707070;
      }
      .nut-picker-cancel-btn,
      .nut-picker-title,
      .nut-picker-confirm-btn {
        font-size: 14px;
        font-weight: 400;
      }
      .nut-picker-confirm-btn {
        font-weight: 500;
        color: #f39800;
      }
      .nut-picker-roller-item-title {
        font-size: 12px;
      }
    }

    .inputAreaRow {
      display: flex;
      justify-content: space-between;
    }
    .areaRow {
      position: relative;
      // background-color: pink;
      padding: 0 !important;
      margin-bottom: 0 !important;
      > .nut-cell-divider {
        display: none;
      }
    }
    .dropDownIcon {
      position: absolute;
      right: 20px;
      top: 25px;
      // background-color: red;
      z-index: 9999;
    }

    .nut-cell-divider {
      border-color: #BEBEBEA3;

    }
  }
}
