.myApply {
  .listContent {
    padding: 16px;
    height: 100%;
    box-sizing: border-box;
    overflow: scroll;

    .MyApplyList {
      margin-bottom: 16px;
      border-radius: 4px;
    }

  }

  .noData {
    height: 100%;
    display: flex;
    justify-content: center;
    padding-top: 209px;


  }

  .content {
    display: flex;
    flex-direction: column;
    background-color: #fff;
    .searchBarArea {
      padding: 10px 12px 16px;
      display: flex;
      .filterIcon {
        width: 40px;
        height: 40px;
        margin-left: 10px;
        display: flex;
        justify-content: center;
        align-items: center;
        border: 1px solid #BEBEBEA3;
        box-sizing: border-box;
        border-radius: 4px;
      }
      .searchBar {
        flex: 1;
      }

    }
    .searchContent {
      flex: 1;
      overflow-y: scroll;
      background-color: #F7F8FA;
    }


  }

}
