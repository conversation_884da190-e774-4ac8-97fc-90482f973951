.MyApplyList {
  position: relative;
  background-color: #fff;

  .topArea {
    padding: 16px;

    .status {
      font-size: 12px;
      font-weight: 500;
      line-height: 16px;
      border-radius: 0 4px 0 0;
      position: absolute;
      right: 0;
      top: 0;
      padding: 4px 8px;
    }

    .apply {
      background-color: rgba(42, 103, 255, 0.16);
      color: #2a67ff;
    }

    .going,
    .awaitStorage {
      background-color: rgba(253, 169, 17, 0.16);
      color: #fda911;
    }

    .hasApprove {
      background-color: rgba(95, 184, 135, 0.16);
      color: #5fb887;
    }

    .view {
      background-color: rgba(17, 17, 17, 0.1);
      color: #111111;
    }

    .titleRow {
      display: flex;
      font-size: 14px;
      font-weight: 500;
      align-items: center;

      .titleLabel {
        font-size: 10px;
        line-height: 12px;
        border-radius: 0 4px 0 4px;
        background-color: rgba(104, 113, 128, 1);
        color: #fff;
        padding: 2px 6px;
        margin-right: 4px;
      }
    }
    .operateRow {
      margin-top: 10px;
      display: flex;
      align-items: center;
      .operate {
        line-height: 16px;
        font-size: 12px;
        line-height: 16px;
        color: #707070;
      }
      .lastOperate {
        margin-left: 16px;
      }
    }

    .operatePeople {
      margin-top: 4px;
      line-height: 16px;
      font-size: 12px;
      line-height: 16px;
      color: #707070;
    }
  }

  .bottomRow {
    height: 52px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
    color: #999999;
    border-top: 1px solid #f0f0f0;
    padding: 0 16px;

    .bottomBtn {
      font-size: 14px;
      line-height: 20px;
      color: #f39800;
      border-bottom: 1px solid #f39800;
    }
  }
}
