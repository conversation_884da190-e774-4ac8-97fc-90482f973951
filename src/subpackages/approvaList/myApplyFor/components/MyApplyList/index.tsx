import { View } from '@tarojs/components'

import { statusText, orderStatus } from '@/utils/type'
import { useEffect, useState } from 'react'
import Taro from '@tarojs/taro'

import './index.scss'

const MyApplyList = ({ info, tabV }) => {
  const [operateType, setOperateType] = useState<string>('')
  const [statusStyle, setStatusStyle] = useState('')

  useEffect(() => {
    if (info.djlx == 2) {
      setOperateType(info.is_xn == 0 ? '实物归还' : '异常核销归还')
    } else {
      setOperateType(orderStatus[info.djlx])
    }
    const keyV = Object.keys(statusText).find((key) => statusText[key] == info.zt)
    setStatusStyle(keyV)
  }, [info])

  const onClickBtn = () => {
    if (info.zt == '待提交') {
      Taro.navigateTo({
        url: `/subpackages/approvaList/materialApply/index?id=${info.Id}&type=${info.djlx}`,
      })
    } else {
      Taro.navigateTo({
        url: `/subpackages/approvaList/applyForDetail/index?id=${info.Id}&type=${info.djlx}&is_xn=${info.is_xn}`,
      })
    }
  }

  return (
    <View className='MyApplyList'>
      <View className='topArea'>
        <View className={['status', statusStyle].join(' ')}>{info.zt}</View>
        <View className='titleRow'>
          <View className='titleLabel'>{operateType}</View>
          {info.djbh}
        </View>
        <View className='operateRow'>
          <View className='operate'>
            {info.djlx == 2 ? '归还' : '申请'}
            数量: {info.sl}
          </View>
          {tabV == 4 && <View className='operate lastOperate'>审批数量: {info.sl_1}</View>}
        </View>

        <View className='operatePeople'>提交人：{info.zdr}</View>
      </View>

      <View className='bottomRow'>
        {info.zdrq}
        <View className='bottomBtn' onClick={() => onClickBtn()}>
          {info.zt == '待提交' ? '编辑' : '查看'}
        </View>
      </View>
    </View>
  )
}
export default MyApplyList
