import { View } from '@tarojs/components'
import { useDidShow } from '@tarojs/taro'
import { MainLayout } from '@/components'
import { Tabs } from '@nutui/nutui-react-taro'
import TabsUI from '@/subpackages/approvaList/components/Tabs'
import { observer } from 'mobx-react'
import { useEffect, useState } from 'react'
import { getNavBarInfo } from '@/utils'
import http from '@/http'
import NoOrder from '@/components/NoOrder'
import SearchBar from '@/components/SearchBar'
import ApplyFilterPopup from '@/components/ApplyFilterPopup'
import IconFont from '@/components/iconfont'
import MyApplyList from './components/MyApplyList'


import './index.scss'

const MyApply = () => {
  const { navBarAndStatusBarHeight: barHeight } = getNavBarInfo()
  const [tabValue, setTabValue] = useState<number>(3)

  const [list, setList] = useState([])
  const [loading, setLoading] = useState(false)
  const [filterOptions, setFilterOptions] = useState({})
  const [filterPopupShow,setFilterPopupShow] = useState(false)
  const [searchV,setSearchV] = useState('')

  

  useEffect(() => {
    getList()
  }, [tabValue, filterOptions,searchV])

  useDidShow(() => {
    getList()
  })

  const getList = async () => {
    setLoading(true)
    setList([])
    const res = await http.common.default.post(
      {
        keyword: searchV,
        ...filterOptions,
        type: 1,
        status: tabValue,
      },
      {},
      '?c=zdwld&m=list'
    )
    setLoading(false)

    if (res.code == 0) {
      setList(res.data.list)
    }
  }

  const onChangeFilterOptions = (params) => {
    setFilterPopupShow(false)
    setFilterOptions(params)
  } 
  const onChangeInputSearch = (v) => {
    setSearchV(v)
  }

  return (
    <MainLayout
      className='myApply'
      initOptions={{
        inited: true,
        initLoading: true,
      }}
      headBarConfig={{
        showBack: true,
        showSearch: false,
        backgroundColor: '#fff',
        color: '#000',
        headerTitle: '我的申请',
        icon: '',
      }}
      style={{ backgroundColor: '#F7F8FA' }}
      showHeaderBar
      showTabBar={false}
    >
      <View className='content' style={{ height: `calc(100vh - ${barHeight}px)` }}>
        <View className='searchBarArea'>
          <SearchBar focus={false} disabled={false} onSearch={onChangeInputSearch} />
          <View className='filterIcon' onClick={()=> setFilterPopupShow(true)}>
            <IconFont name='sort' size={24} />
          </View>
        </View>
        <TabsUI
          value={tabValue}
          onChange={(value) => {
            setTabValue(value)
          }}
        >
          <Tabs.TabPane value={3} title='进行中'></Tabs.TabPane>
          <Tabs.TabPane value={4} title='已结束'></Tabs.TabPane>
        </TabsUI>

        <View className='searchContent'>
          {list?.length || loading ? (
            <View className='listContent'>
              {list.map((item) => {
                return <MyApplyList key={item.Id} info={item} tabV={tabValue} />
              })}
            </View>
          ) : (
            <View className='noData'>
              <NoOrder
                title={tabValue == 3 ? '暂无进行中申请' : '暂无已结束申请'}
                imgType={tabValue == 3 ? 'applyGoing' : 'applyEnd'}
              />
            </View>
          )}
        </View>

        <ApplyFilterPopup visible={filterPopupShow} setVisible={setFilterPopupShow}  onChange={onChangeFilterOptions} type='apply' />
      </View>
    </MainLayout>
  )
}

export default observer(MyApply)
