import { View } from '@tarojs/components'
import { MainLayout } from '@/components'
import { useEffect, useState } from 'react'
import { useRequest } from 'ahooks'

import { Dialog, Button } from '@nutui/nutui-react-taro'

import BaseInfo from '@/subpackages/approvaList/applyForDetail/BaseInfo'
import CanEditGood from '@/subpackages/approvaList/components/CanEditGood'
import TitleRow from '@/subpackages/approvaList/components/TitleRow'
import IconFont from '@/components/iconfont'
import { getNavBarInfo, onSubscribeMessage } from '@/utils'
import Taro, { useRouter, useDidShow } from '@tarojs/taro'
import http from '@/http'
import { useStores } from '@/hook'
import { observer } from 'mobx-react'
import _ from 'lodash'

import cartUtil from '@/utils/cart'
import { orderStatus } from '@/utils/type'

import './index.scss'

const MaterialApply = () => {
  const { navBarAndStatusBarHeight: barHeight, bottomBarHeight } = getNavBarInfo()
  const { materialApplyStore } = useStores()
  const { goodList, baseInfo } = materialApplyStore
  const router = useRouter()
  const [routeParams] = useState(router.params)
  const [endVisible, setEndVisible] = useState(false)
  const [loading, setLoading] = useState(false)

  useDidShow((res) => {
    getGoodsInfo()
  })

  useEffect(() => {
    getHeadInfo()
  }, [routeParams.id])

  useEffect(() => {
    const total = goodList.reduce((prev, item) => {
      return prev + Number(item.sl)
    }, 0)
    materialApplyStore.setBaseInfo({ ...baseInfo, sl: total })
  }, [goodList])

  const getHeadInfo = async () => {
    const res = await http.common.default.post(
      {
        dj_id: routeParams.id,
      },
      {},
      '?c=zdwld&m=base_info'
    )
    if (res.code == 0) {
        materialApplyStore.setBaseInfo(res.data)

      // if (res.data.djlx == 2 ) {
      //   materialApplyStore.setBaseInfo(res.data)
      // } else {
      //   materialApplyStore.setBaseInfo({
      //     ...res.data,
      //     gkname: '',
      //     phone: '',
      //     province: '',
      //     city: '',
      //     district: '',
      //     address: '',
      //   })
      // }
    }
  }

  const getGoodsInfo = async () => {
    const res = await http.common.default.post(
      {
        dj_id: routeParams.id,
      },
      {},
      '?c=zdwld&m=wlsp_list'
    )
    if (res.code == 0) {
      materialApplyStore.setGoodList(res.data)
    }
  }

  const onUpdateSl = (info) => {
    const res = cartUtil.onUpdateGoodSl(info, goodList)
    materialApplyStore.setGoodList(res)
  }

  const onDeleteGood = async (id) => {
    // const res = cartUtil.onDeleteGoods(info, goodList)
    // materialApplyStore.setGoodList(res)
    console.log('----info', id)

    const ress = await http.common.default.post(
      {
        dj_id: baseInfo?.dj_id,
        mx_id: id,
      },
      {},
      '?c=zdwld&m=delect_skus'
    )

    if (ress.code == 0) {
      getGoodsInfo()
    } else {
      Taro.showToast({
        title: ress?.msg,
        icon: 'none',
        duration: 2000,

      })
    }
  }

  const onDetailSubmitData = async () => {
    setLoading(true)
    const list = goodList.map((item) => ({ barcode: item.barcode, sl: item.sl }))

    const res = await http.common.default.post(
      {
        dj_header: JSON.stringify(baseInfo),
        mx: JSON.stringify(list),
      },
      {},
      '?c=zdwld&m=update_zdwld'
    )
    if (res.code != 0) {
      Taro.showToast({
        title: res.msg,
        icon: 'none',
        duration: 2000,

      })
      setLoading(false)
      return
    }

    await http.common.default.post({ dj_id: baseInfo.dj_id }, {}, '?c=zdwld&m=sp_tj')
    Taro.redirectTo({
      url: `/subpackages/approvaList/applyForDetail/index?id=${baseInfo.dj_id}&type=${baseInfo.djlx}&is_xn=${baseInfo.is_xn}`,
    })
    setLoading(false)
  }

  const { run: onSubmit } = useRequest(
    async () => {
      console.log('---baseInfo', baseInfo)
      if (
        !goodList.length ||
        (!baseInfo?.shipping_sn && baseInfo?.is_xn == 0 && baseInfo?.djlx == 2)
      ) {
        !goodList.length
          ? Taro.showToast({
              title: '请至少选择一种物料',
              icon: 'none',
              duration: 2000,

            })
          : Taro.showToast({
              title: '物流单号不能为空',
              icon: 'none',
              duration: 2000,

            })
      } else {
        if (loading) return
        onSubscribeMessage(onDetailSubmitData, () => {})
      }
    },
    {
      throttleWait: 1000,
      manual: true,
    }
  )

  const onConfirmEnd = async () => {
    const res = await http.common.default.post(
      {
        dj_id: baseInfo.dj_id,
      },
      {},
      '?c=zdwld&m=zz'
    )
    if (res.code == 0) {
      Taro.showToast({
        icon: 'none',
        title: '终止成功',
        success: () => {
          setEndVisible(false)
          setTimeout(() => {
            Taro.switchTab({ url: '/pages/home/<USER>' })
          }, 1000)
        },
      })
    } else {
      Taro.showToast({
        title: res.msg,
        icon: 'error',
        duration: 2000,

      })
    }
  }

  console.log('------routeParams.type', routeParams.type)

  return (
    <MainLayout
      className='materialApply'
      initOptions={{
        inited: true,
        initLoading: true,
      }}
      headBarConfig={{
        showBack: true,
        showSearch: false,
        backgroundColor: '#fff',
        color: '#000',
        headerTitle: ['2', '3'].includes(routeParams.type) ? '物料归还明细' : '物料申请明细',
        icon: '',
      }}
      style={{ backgroundColor: '#F7F8FA' }}
      showHeaderBar
      showTabBar={false}
    >
      <View
        className='content'
        style={{ height: `calc(100vh - ${barHeight}px - ${bottomBarHeight}px)` }}
      >
        <BaseInfo />

        <View className='materialTitle'>
          <TitleRow
            leftV='物料明细'
            rightV={
              <View
                className='addR'
                onClick={() =>
                  Taro.navigateTo({
                    url: `/subpackages/approvaList/materialAdd/index?type=${routeParams.type}&id=${routeParams.id}`,
                  })
                }
              >
                <IconFont
                  name='a-Group1561267575'
                  color='#F39800'
                  size={10}
                  style={{ marginRight: Taro.pxTransform(4) }}
                />
                添加物料
              </View>
            }
          ></TitleRow>
        </View>
        <View className='materialContent'>
          {goodList.map((item) => {
            return (
              <CanEditGood
                key={item.barcode}
                info={item}
                onUpdateSl={onUpdateSl}
                onDelete={onDeleteGood}
                type={routeParams.type}
              />
            )
          })}
        </View>
        <View className='btns' style={{ paddingBottom: bottomBarHeight + 'px' }}>
          <View className='cancel btn' onClick={() => setEndVisible(true)}>
            终止
          </View>

          <View className='sure btn' onClick={onSubmit}>
            提交{baseInfo?.is_xn == 0 && baseInfo?.djlx == 2 ? '归还' : '审批'}
          </View>
        </View>

        <Dialog
          title={`${orderStatus[routeParams.type]}终止`}
          visible={endVisible}
          onConfirm={onConfirmEnd}
          onCancel={() => setEndVisible(false)}
          closeIcon={<IconFont name='a-Frame427320086' size={24} color='#000' />}
          closeIconPosition='top-right'
        >
          确定终止{orderStatus[routeParams.type]}?
        </Dialog>
      </View>
    </MainLayout>
  )
}

export default observer(MaterialApply)
