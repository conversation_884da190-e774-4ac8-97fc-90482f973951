.materialApply {
  .content {
    display: flex;
    flex-direction: column;
    overflow: scroll;
    padding-bottom: calc(env(safe-area-inset-bottom) * var(--nutui-safe-area-multiple, 1) + 50px);
    box-sizing: border-box;

    .addR {
      display: flex;
      align-items: center;
      font-size: 12px;
      color: #f39800;
    }

    .materialTitle {
      padding: 12px 24px 0;
      margin-top: 8px;
      background-color: #fff;
    }

    .materialContent {
      background-color: #fff;
    }

    .btns {
      position: absolute;
      left: 0;
      bottom: 0;
      background-color: #fff;

      width: 100%;
      padding: 9px 24px 0;
      padding-bottom: calc(env(safe-area-inset-bottom) * var(--nutui-safe-area-multiple, 1));
      display: flex;
      justify-content: space-between;
      align-items: center;
      box-sizing: border-box;
      box-shadow: 0px 0px 8px 0px #0000000a;
      z-index: 1200;

      .btn {
        flex: 1;
        height: 40px;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 14px;
        border-radius: 4px;
      }

      .cancel {
        margin-right: 12px;
        border: 1px solid #bebebe;
        color: #111111;
        background-color: #fff;
      }
      .sure {
        color: #fff;
        background-color: #f39800;
      }
    }
  }

  .nut-dialog-outer {
    border-radius: 4px;
    width: calc(100% - 40px);
    .nut-dialog {
      padding: 24px 16px;
      width: 100%;
      .nut-dialog-header {
        font-size: 16px;
        line-height: 24px;
      }
      .nut-dialog-content {
        font-size: 14px;
        line-height: 20px;
        color: #707070;
        margin: 20px 0 24px;
      }
      .nut-dialog-footer {
        .nut-button {
          width: 140px;
          height: 40px;
          padding: 0;
          box-sizing: border-box;
          border-radius: 4px;
          font-size: 13px;
          &:first-child {
            margin-right: 15px;
          }
        }
        .nut-dialog-footer-cancel {
          border: 1px solid #bebebe;
        }
        .nut-button-primary {
          background: #f39800 !important;
        }
      }
    }
  }
}
