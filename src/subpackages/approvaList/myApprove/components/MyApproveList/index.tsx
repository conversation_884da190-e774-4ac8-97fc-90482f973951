import React, { useEffect, useState } from 'react'

import { View } from '@tarojs/components'
import { orderStatus } from '@/utils/type'
import Taro from '@tarojs/taro'

import './index.scss'

const MyApproveList = ({ info }) => {
  const [operateType, setOperateType] = useState<string>('')
  useEffect(() => {
    if (info.djlx == 2) {
      setOperateType(info.is_xn == 0 ? '实物归还' : '异常核销归还')
    } else {
      setOperateType(orderStatus[info.djlx])
    }
  }, [info])

  
  const jump = () => {
    if (['已完成', '已审批','已拒绝'].includes(info.zt)) {
      Taro.navigateTo({
        url: `/subpackages/approvaList/applyForDetail/index?id=${info.Id}&type=${info.djlx}&is_xn=${info.is_xn}`,
      })
    } else {
      Taro.navigateTo({
        url: `/subpackages/approvaList/materialApprove/index?id=${info.Id}&type=${info.djlx}&is_xn=${info.is_xn}`,
      })
    }
  }

  return (
    <View className='myApproveList'>
      <View className={['titleRow', ['已完成', '已审批'].includes(info.zt)].join(' ')}>
        <View className='titleLeft'>
          <View className='titleLabel '>{operateType}</View>
          {info.djbh}
        </View>
        <View className='titleRight'>{info.zdrq}</View>
      </View>
      <View className='partRow'>
        <View className='locationLeft'>
          <View className='location'>申请门店/部门：{info.zdmc}</View>
          <View className='name'>提交人：{info.zdr}</View>
        </View>
        <View
          className='btn'
          onClick={jump}
        >
          {['已完成', '已审批','已拒绝'].includes(info.zt) ? '查看' : '去审批'}
        </View>
      </View>
    </View>
  )
}

export default MyApproveList
