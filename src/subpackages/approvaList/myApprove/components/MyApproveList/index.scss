.myApproveList {
  padding: 16px;
  border-radius: 4px;
  background-color: #FFFFFF;
  margin-bottom: 16px;

  .titleRow {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .titleLeft {
      font-size: 14px;
      font-weight: 500;
      color: #000;
      display: flex;
      align-items: center;

      .titleLabel {
        font-size: 10px;
        color: #fff;
        background-color: #687180;
        padding: 2px 6px;
        border-radius: 0 4px 0 4px;
        margin-right: 4px;

      }

    }

    .return {
      background-color: rgba(104, 113, 128, 0.64);

    }

    .titleRight {
      color: #999999;
      font-size: 12px;

    }


  }

  .return {
    color: rgba(112, 112, 112, 1);
    .titleLeft {
    color: rgba(112, 112, 112, 1);

      
      .titleLabel {

        background-color: rgba(104, 113, 128, 0.64);

      }

    }
  }

  .partRow {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 10px;

    .locationLeft {

      .location,
      .name {
        color: #707070;
        font-size: 12px;
        line-height: 16px;
      }

      .name {
        margin-top: 4px;
      }









    }

    .btn {
      padding: 4px 8px;
      color: #fff;
      font-size: 12px;
      line-height: 16px;
      background-color: #F39800;
      border-radius: 4px;

    }





  }












}
