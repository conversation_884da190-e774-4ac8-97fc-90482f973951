import { View } from '@tarojs/components'
import { MainLayout } from '@/components'
import { SafeArea, Tabs } from '@nutui/nutui-react-taro'

import TabsUI from '@/subpackages/approvaList/components/Tabs'

import { useState, useEffect } from 'react'

import { getNavBarInfo } from '@/utils'
import http from '@/http'
import NoOrder from '@/components/NoOrder'
import { useDidShow } from '@tarojs/taro'
import SearchBar from '@/components/SearchBar'
import IconFont from '@/components/iconfont'
import ApplyFilterPopup from '@/components/ApplyFilterPopup'


import MyApproveList from './components/MyApproveList'

import './index.scss'

const MaterialReturn = () => {
  const { navBarAndStatusBarHeight: barHeight } = getNavBarInfo()
  const [tabValue, setTabValue] = useState<number>(3)
  const [list, setList] = useState([])
  const [loading, setLoading] = useState(false)

  const [filterOptions, setFilterOptions] = useState({})
  const [filterPopupShow, setFilterPopupShow] = useState(false)
  const [searchV, setSearchV] = useState('')

  useEffect(() => {
    getList()
  }, [tabValue,filterOptions,searchV])

  useDidShow(() => {
    getList()
  })

  const getList = async () => {
    setLoading(true)
    setList([])
    const res = await http.common.default.post(
      {
        keyword: searchV,
        ...filterOptions,
        type: 2,
        status: tabValue,
      },
      {},
      '?c=zdwld&m=list'
    )
    setLoading(false)
    if (res.code == 0) {
      setList(res.data.list)
    }
  }

   const onChangeFilterOptions = (params) => {
    setFilterPopupShow(false)
    setFilterOptions(params)
  }

   const onChangeInputSearch = (v) => {
    setSearchV(v)
  }


  return (
    <MainLayout
      className='myApprove'
      initOptions={{
        inited: true,
        initLoading: true,
      }}
      headBarConfig={{
        showBack: true,
        showSearch: false,
        backgroundColor: '#fff',
        color: '#000',
        headerTitle: '我的审批',
        icon: '',
      }}
      style={{ backgroundColor: '#F7F8FA' }}
      showHeaderBar
      showTabBar={false}
    >
      <View className='content' style={{ height: `calc(100vh - ${barHeight}px)` }}>
        <View className='searchBarArea'>
          <SearchBar focus={false} disabled={false} onSearch={onChangeInputSearch} />
          <View className='filterIcon' onClick={() => setFilterPopupShow(true)}>
            <IconFont name='sort' size={24} />
          </View>
        </View>

        <TabsUI
          value={tabValue}
          onChange={(value) => {
            setTabValue(value)
          }}
        >
          <Tabs.TabPane value={3} title='进行中'></Tabs.TabPane>
          <Tabs.TabPane value={4} title='已处理'></Tabs.TabPane>
        </TabsUI>

        <View className='searchContent'>
          {list.length || loading ? (
            <View className='listContent'>
              {list.map((item) => {
                return <MyApproveList key={item.Id} info={item} />
              })}
            </View>
          ) : (
            <View className='noData'>
              <NoOrder
                title={tabValue == 3 ? '暂无待审批流程' : '暂无已处理流程'}
                imgType={tabValue == 3 ? 'approveGoing' : 'approveEnd'}
              />
            </View>
          )}
        </View>
        <ApplyFilterPopup visible={filterPopupShow} setVisible={setFilterPopupShow}  onChange={onChangeFilterOptions} type='approve' />
      </View>
    </MainLayout>
  )
}

export default MaterialReturn
