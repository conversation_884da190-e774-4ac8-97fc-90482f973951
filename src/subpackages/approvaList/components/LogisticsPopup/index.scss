.logisticsPopup {
  position: relative;
  .nut-popup {
    overflow-y: inherit;
    padding: 24px 20px !important;
    width: calc(100vw - 40px);
    height: 280px;
    box-sizing: border-box;
    margin: 0 auto;
    border-radius: 4px;

    .logisticsPopupContent {
      width: 100%;
      height: 100%;
      position: relative;
      .row {
        .mainTitle {
          font-weight: 400;
          font-size: 16px;
          line-height: 24px;
          text-align: center;
        }
        .closeIcon {
          top: 0;
          right: 0;
          position: absolute;
          z-index: 1;
        }
      }

      .title {
        margin-top: 20px;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
      }
      .company {
        display: flex;
        justify-content: space-between;
        align-items: center;
        border: 1px solid #f0f0f0;
        height: 40px;
        min-height: 40px;
        box-sizing: border-box;
        padding: 0 12px 0 16px;
        margin-top: 8px;
        color: #000;
        position: relative;
        .leftName {
          color: #000000;
          font-weight: 400;
          font-size: 14px;
          line-height: 20px;
          text-align: center;
        }
      }
      .selectArea {
        position: absolute;
        left: 0;
        top: 120px;
        background-color: #fff;
        width: 100%;
        z-index: 1201;
        height: 300px;
        overflow: scroll;
        border: 1px solid #f0f0f0;
        border-radius: 4px;
        .nut-radiogroup {
          padding: 0 16px;
          box-sizing: border-box;

          .nut-radio {
            margin: 0;
            justify-content: space-between;
            height: 60px;
            border-bottom: 1px solid #e1e1e1;
            .nut-radio-label {
              font-weight: 400;
              font-size: 14px;
              line-height: 20px;
              color: #212121;
            }
          }
        }
      }

      .number {
        margin-top: 16px;
        height: 40px;
        .nut-input {
          height: 100%;
          padding: 10px 16px;
          border: 1px solid #f0f0f0;
          color: #000;
          font-size: 14px;

          .input-placeholder {
            font-weight: 400;
            font-size: 14px;
            line-height: 20px;
            color: #999999;
          }
        }
      }

      .btns {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0;

        .btn {
          flex: 1;
          height: 40px;
          border-radius: 4px;
          font-weight: 400;
          font-size: 13px;
          line-height: 20px;
          text-align: center;
        }
      }
    }
  }
}
