import { View } from '@tarojs/components'
import { Popup, Input, Radio } from '@nutui/nutui-react-taro'
import IconFont from '@/components/iconfont'
import { useEffect, useState } from 'react'
import http from '@/http'
import { observer } from 'mobx-react'
import { useStores } from '@/hook'
import Taro from '@tarojs/taro'

import './index.scss'

interface CompanyListType {
  shipping_id: string
  shipping_name: string
  logistics_code: string
}

const LogisticsPopup = ({ visible, setVisible }) => {
  const [show, setShow] = useState(false)
  const [companyList, setCompanyList] = useState<CompanyListType[]>([])
  const { materialApplyStore } = useStores()
  const { baseInfo } = materialApplyStore

  const [shippingId, setShippingId] = useState('')
  const [shippingName, setShippingName] = useState('请选择')
  const [shippingNumber, setShippingNumber] = useState('')

  useEffect(() => {
    if (show && !companyList.length) {
      getLogisticsList()

    }
  }, [show])



  const getLogisticsList = async () => {
    const res = await http.common.default.post({}, {}, '?c=zdwld&m=shipping')
    if (res.code == 0) {
      setCompanyList(res.data.list)
    }
  }
  const onChangeCompany = (v: string) => {
    setShippingId(v)
    setShow(false)
    const res = companyList.filter((item) => item.shipping_id == v)
    setShippingName(res[0]?.shipping_name)
  }

  const onSure = () => {
    if (!shippingId) {
      Taro.showToast({
        title: '请选择物流公司',
        icon: 'none',
      })
      return
    }
    if (!shippingNumber) {
      Taro.showToast({
        title: '请输入物流单号',
        icon: 'none',
      })
      return
    }
    const res = companyList.filter((item) => item.shipping_id == shippingId)

    materialApplyStore.setBaseInfo({ ...baseInfo, ...res[0],shipping_sn:shippingNumber })
    setVisible(false)
  }

  return (
    <View className='logisticsPopup'>
      <Popup
        visible={visible}
        style={{ padding: '30px 50px' }}
        zIndex={1201}
        onClose={() => {
          // setShowBasic(false)
        }}
      >
        <View className='logisticsPopupContent'>
          <View className='row'>
            <View className='mainTitle'>物流信息编辑</View>
            <View className='closeIcon' onClick={() => setVisible(false)}>
              <IconFont name='a-Frame427320086' size={24} />
            </View>
          </View>

          <View className='title'>选择物流公司</View>
          <View className='company' onClick={() => setShow(!show)}>
            <View
              className='leftName'
              style={{ color: shippingName == '请选择' ? '#999999' : '#000000' }}
            >
              {shippingName}
            </View>
            <View>
              <IconFont name='icon' size={20} />
            </View>
          </View>

          <View className='selectArea' style={{ display: show ? 'block' : 'none' }}>
            <Radio.Group
              defaultValue=''
              labelPosition='left'
              style={{ width: '100%' }}
              onChange={onChangeCompany}
            >
              {companyList.map((item, index) => {
                return (
                  <Radio
                    key={item.shipping_id}
                    icon={<IconFont name='weixuanze1' size={16} color='#2D2E2C' />}
                    activeIcon={<IconFont name='checked' size={16} color='#F39800' />}
                    value={item.shipping_id}
                  >
                    {item.shipping_name}
                  </Radio>
                )
              })}
            </Radio.Group>
          </View>
          <View className='number'>
            <Input
              value={shippingNumber}
              placeholder='请输入物流单号'
              onChange={(v) => {
                setShippingNumber(v)
              }}
            />
          </View>
          <View className='btns'>
            <View className='cancel btn' onClick={() => setVisible(false)}>
              取消
            </View>
            <View className='sure btn' onClick={onSure}>
              确定
            </View>
          </View>
        </View>
      </Popup>
    </View>
  )
}

export default observer(LogisticsPopup)
