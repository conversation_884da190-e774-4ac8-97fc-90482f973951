import { View } from '@tarojs/components'
import { Image, InputNumber } from '@nutui/nutui-react-taro'
import IconFont from '@/components/iconfont'
import { useEffect, useState } from 'react'

import { GoodType } from '@/components/MaterialInfo'
import MInputNumber from '@/components/MInputNumber'

import './index.scss'

interface CanEditGoodType {
  info: GoodType[]
  onUpdateSl: (v: GoodType[]) => void
  onDelete: (v) => void
}

const CanEditGood = ({ type,info, onUpdateSl, onDelete }) => {
  const [totalPrice, setTotalPrice] = useState(0)
  
  

  useEffect(() => {
    if (info.goods_price && info.sl) {
      setTotalPrice(info.goods_price * info.sl)
    } else {
      setTotalPrice(0)
    }
  }, [info.goods_price, info.sl])

  return (
    <View className='operate-goods-item canEditGood'>
      <View className='goods-pic'>
        <Image
          src={info.img_url}
          mode='aspectFill'
          style={{
            width: '100%',
            height: '100%',
          }}
        ></Image>
      </View>
      <View className='goods-info-operate'>
        <View className='operate-top'>
          <View className='goods-left-info'>
            <View className='sku'>{info.goods_name}</View>
            <View className='goods-size'>
              规格：{info.color_name}/{info.size_name}
            </View>
          </View>
          <View className='del-btn' onClick={() => onDelete(info.id)}>
            <IconFont name='delete' size={20} color='#1C1B1F'></IconFont>
          </View>
        </View>
        <View className='operate-btm'>
          <View className='goods-left-price'>
            <View className='unit-price'>参考价¥{info.goods_price}</View>
            <View className='total-price'>¥{totalPrice}</View>
          </View>
          <View className='count-operate'>
            <MInputNumber
              min={1}
              max={[0, 1,'0','1'].includes(type) ? Number(info.kysl) : Number(info.dgh_sl)}
              value={info.sl}
              onInput={(param) => {
                const number = param ?? 1
                onUpdateSl({ ...info, sl: number })
              }}
            />
          </View>
        </View>
      </View>
    </View>
  )
}
export default CanEditGood
