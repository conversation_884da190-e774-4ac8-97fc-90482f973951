.operate-goods-item.canEditGood {
  display: flex;
  align-items: center;
  padding: 16px 24px;

  &:not(:first-child) {
    border-top: 0.5px solid #f0f0f0;
  }
  .goods-pic {
    width: 104px;
    height: 104px;
    padding: 7px;
    box-sizing: border-box;
  }
  .goods-info-operate {
    flex: 1;
    margin-left: 12px;
    .operate-top {
      display: flex;
      justify-content: space-between;
      .goods-left-info {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        .sku {
          color: #000000;
          font-weight: 700;
          font-size: 12px;
          line-height: 16px;
        }
        .goods-size {
          display: flex;
          align-items: center;
          height: 28px;
          margin-top: 4px;
          padding: 0 10px;
          background-color: #f6f6f6;
          color: #969696;
          font-family: Noto Sans CJK SC;
          font-weight: 400;
          font-size: 12px;
          line-height: 16px;
          border-radius: 4px;
        }
      }
      .del-btn {
        margin-top: -3px;
      }
    }
    .operate-btm {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 16px;
      .goods-left-price {
        .unit-price {
          color: #e34d59;
          font-family: Matter;
          font-weight: 400;
          font-size: 12px;
          line-height: 18px;
          text-transform: capitalize;
        }
        .total-price {
          color: #000000;
          font-family: Noto Sans SC;
          font-weight: 500;
          font-size: 14px;
          line-height: 20px;
        }
      }
      .count-operate {
				

        .minus {
          width: 30px;
          padding-left: 5px;
        }
        .add {
          width: 30px;
          padding-right: 5px;
        }
        .myInput {
          width: 30px;
          .nut-input-native {
            // text-align: center !important;
          }
        }
      }
    }
  }
}
