.tabsContainer{
    --nutui-tabs-titles-item-color:#999;
    --nutui-tabs-titles-height: 32px;
    --nutui-tabs-titles-background-color: #FFFFFF;
    --nutui-tabs-titles-font-size: 14px;
    --nutui-tabs-titles-item-active-color: #F39800;
    --nutui-tabs-titles-item-active-font-weight: 500;
    --nutui-tabs-titles-item-active-font-size: 14px;
    --nutui-tabs-tab-line-width: 42px;
    --nutui-tabs-tab-line-height: 2px;
    --nutui-tabs-tab-line-color: #F39800;
    --nutui-tabs-line-bottom: 0;
    --nutui-gray-1: transparent;
    --nutui-tabs-tabpane-padding: 0;
    background-color: rgba(247, 248, 250, 1);
    --nutui-tabs-tabpane-backgroundColor: rgba(247, 248, 250, 1);
    .nut-tabs {
        display: flex;
        flex-direction: column;
        height: 100%;
        .nut-tabs-content-wrap {
            flex: 1 0 0;
        }
    }
    .nut-tabs-content {
        height: 100%;
        .nut-tabpane {
            overflow: auto;
            height: 100%;
        }
    }

    .noDataWrap{
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 560px;
        height: 560px;
        margin: 112px auto 0;
        .noDataTxt{
            display: flex;
            align-items: center;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 28px;
            color: rgba(41, 41, 41, 1);
            line-height: 24px;
        }
        .toFollow{
            display: flex;
            align-items: center;
            color: rgba(56, 96, 244, 1);
        }
    }
}
