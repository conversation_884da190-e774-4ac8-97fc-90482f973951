import { useState } from 'react'
import { View } from '@tarojs/components'
import { Tabs } from '@nutui/nutui-react-taro'
import { observer } from 'mobx-react'
import './index.scss'

const Index = (props) => {
  return (
    <View className='tabsContainer'>
      <Tabs
        value={props.value}
        onChange={(value) => {
          props.onChange && props.onChange(value)
        }}
      >
        {props.children}
      </Tabs>
    </View>
  )
}
export default observer(Index)
