import Taro, { usePageScroll, useRouter } from '@tarojs/taro'
import { Button, Text, View, ScrollView } from '@tarojs/components'
import { TextArea, Dialog } from '@nutui/nutui-react-taro'
import { observer } from 'mobx-react'
import { useState, useEffect } from 'react'
import { useStores } from '@/hook'
import http from '@/http'
import classNames from 'classnames'
import BaseInfo from '@/subpackages/approvaList/applyForDetail/BaseInfo'
import GoodsDetail from '@/subpackages/approvaList/applyForDetail/GoodsDetail'
import ApprovalProcess from '@/subpackages/approvaList/applyForDetail/ApprovalProcess'
import MainLayout from '@/components/MainLayout'
import { getNavBarInfo } from '@/utils'
import IconFont from '@/components/iconfont'
import AnchorPoint from '@/components/anchorPoint'
import { useRequest } from 'ahooks'
import _ from 'lodash'

import './index.scss'

const approveWords = {
  reject: '拒绝',
  agree: '同意',
}

const Index = () => {
  const { navBarAndStatusBarHeight: barHeight, bottomBarHeight } = getNavBarInfo()

  const [tabs, setTabs] = useState([
    {
      label: '基本信息',
      value: 'baseInfo',
      path: '#baseInfo',
    },
    {
      label: '物料明细',
      value: 'goodsDetail',
      path: '#goodsDetail',
    },
    {
      label: '审批流程',
      value: 'approvalProcess',
      path: '#approvalProcess',
    },
  ])
  const [curTab, setCurTab] = useState('#baseInfo')

  const { materialApplyStore } = useStores()
  const { goodList, baseInfo } = materialApplyStore

  const router = useRouter()

  const [routeParams] = useState(router.params)
  const [approveProcess, setApproveProcess] = useState([])
  const [domsInfo, setDomsInfo] = useState([])
  const [visible, setVisible] = useState(false)
  const [approveStatus, setApproveStatus] = useState('')
  const [remark, setRemark] = useState('')

  useEffect(() => {
    getHeights()
  }, [])

  const getHeights = () => {
    const query = Taro.createSelectorQuery()
    for (let item of tabs) {
      query.select(`#${item.value}`).boundingClientRect()
    }
    query.exec((rect) => {
      console.log('-----rect', rect)
      setDomsInfo(rect)
    })
  }

  useEffect(() => {
    getHeadInfo()
    getGoodsInfo()
    getApprovalProcess()
  }, [routeParams.id])

  const getHeadInfo = async () => {
    const res = await http.common.default.post(
      {
        dj_id: routeParams.id,
      },
      {},
      '?c=zdwld&m=base_info'
    )
    if (res.code == 0) {
      materialApplyStore.setBaseInfo(res.data)
    }
  }

  const getGoodsInfo = async () => {
    const res = await http.common.default.post(
      {
        dj_id: routeParams.id,
      },
      {},
      '?c=zdwld&m=wlsp_list'
    )
    if (res.code == 0) {
      materialApplyStore.setGoodList(res.data)
    }
  }

  const getApprovalProcess = async () => {
    const res = await http.common.default.post(
      {
        dj_id: routeParams.id,
      },
      {},
      '?c=zdwld&m=sp_lc'
    )
    if (res.code == 0) {
      setApproveProcess(res.data.list)
    }
  }

  const onScroll = (v) => {
    const { scrollTop } = v.detail
    if (scrollTop < 30) {
      setCurTab(domsInfo[0].id)
    }
    const res = domsInfo.find((item) => {
      if (item.top > scrollTop && item.top - scrollTop < 30) {
        return item
      }
    })

    if (res) setCurTab(res.id)
  }
  const onClickRejectBtn = () => {
    setApproveStatus('reject')
    setVisible(true)
  }

  const onClickSubmitBtn = async () => {
    const index = goodList.findIndex((item) =>{
      return  item.sl_1 == ''
    })
    if (index > -1) {
      Taro.showToast({
        title: '物料审批数不能为空',
        icon: 'none',
        duration: 2000,

      })
    } else {
      setApproveStatus('agree')
      setVisible(true)
    }
  }

  const { run: onSubmit } = useRequest(
    async () => {
      if (!remark && approveStatus == 'reject') {
        Taro.showToast({
          title: '请输入备注',
          icon: 'none',
          duration: 2000,

        })
        return
      }
      const list = goodList.map((item) => {
        return {
          barcode: item.barcode,
          sl_1: item.sl_1,
        }
      })
      const res = await http.common.default.post(
        {
          dj_id: routeParams.id,
          spbz: remark,
          spjg: approveStatus == 'reject' ? 2 : 1,
          mx: JSON.stringify(list),
        },
        {},
        '?c=zdwld&m=sp_cz'
      )
      if (res.code == 0) {
        setApproveStatus('')
        setRemark('')
        setVisible(false)
        Taro.redirectTo({
          url: `/subpackages/approvaList/applyForDetail/index?id=${routeParams.id}&type=${routeParams.type}&is_xn=${routeParams.is_xn}`,
        })
      } else {
        Taro.showToast({
          title: res.msg,
          icon: 'none',
          duration: 2000,

        })
      }
    },
    {
      throttleWait: 1000,
      manual: true,
    }
  )

  const onCancel = () => {
    setApproveStatus('')
    setRemark('')
    setVisible(false)
  }

  return (
    <MainLayout
      className='apply-for-detail'
      initOptions={{
        inited: true,
        initLoading: true,
      }}
      headBarConfig={{
        showBack: true,
        showSearch: false,
        backgroundColor: '#fff',
        color: '#000',
        headerTitle: routeParams.type == 2 ? '物料归还明细' : '物料申请明细',
        icon: '',
      }}
      style={{ backgroundColor: '#F7F8FA' }}
      showHeaderBar
      showTabBar={false}
    >
      <View className='content' style={{ height: `calc(100vh - ${barHeight}px ` }}>
        <View className='switch-tabs' id='switch-tabs' style={{ top: `${barHeight}px` }}>
          <AnchorPoint list={tabs} curTab={curTab} setCurTab={setCurTab} />
        </View>

        <View
          className='switch-content'
          style={{ paddingBottom: `calc(${bottomBarHeight}px + ${Taro.pxTransform(60)} )` }}
          // type='list'
          // scrollY
          // scrollIntoView={curTab}
          // scroll-with-animation
          // scrollAnimationDuration='300'
          // onScroll={onScroll}
        >
          <BaseInfo></BaseInfo>
          <GoodsDetail></GoodsDetail>
          <ApprovalProcess info={approveProcess}></ApprovalProcess>
        </View>

        <View className='btns' style={{ paddingBottom: bottomBarHeight + 'px' }}>
          <View className='cancel btn' onClick={onClickRejectBtn}>
            拒绝
          </View>
          <View className='sure btn' onClick={onClickSubmitBtn}>
            同意
          </View>
        </View>

        <Dialog
          title={'审批' + approveWords[approveStatus]}
          visible={visible}
          onConfirm={onSubmit}
          onCancel={onCancel}
          closeIcon={<IconFont name='a-Frame427320086' size={24} color='#000' />}
          closeIconPosition='top-right'
        >
          <TextArea
            placeholder='请输入描述'
            style={{ fontSize: '12px' }}
            value={remark}
            onChange={(value) => setRemark(value)}
          />
        </Dialog>
      </View>
    </MainLayout>
  )
}

export default observer(Index)
