.apply-for-detail {
  background-color: rgba(247, 248, 250, 1);

  .content {
    display: flex;
    flex-direction: column;
  }

  .switch-tabs {
    display: flex;
    justify-content: center;
    width: 100%;

    height: 40px;
    background-color: #fff;
    color: rgba(153, 153, 153, 1);
    font-family: Noto Sans SC;
    font-weight: 400;
    font-size: 14px;
    text-align: center;
    line-height: 20px;
    position: fixed;
    left: 0;
    .tab-item {
      position: relative;
      flex: 1;
      padding-top: 12px;
      &::before {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        width: 0;
        height: 2px;
        background-color: #f39800;
        transform: translateX(-50%);
      }
      &.active {
        color: #f39800;
        font-weight: 500;
        &::before {
          width: 56px;
          transition: 0.2s;
        }
      }
    }
  }
  .switch-content {
    // height: calc(100% - 40px);
    // padding-bottom: calc(env(safe-area-inset-bottom) * var(--nutui-safe-area-multiple, 1) + 50px);
    box-sizing: border-box;
    margin-top: 56px;
    padding-bottom: calc(env(safe-area-inset-bottom) * var(--nutui-safe-area-multiple, 1) + 60px);
    flex: 1;
    
  }

  .btns {
    position: fixed;
    left: 0;
    bottom: 0;
    background-color: #fff;

    width: 100%;
    padding: 9px 24px 0;
    padding-bottom: calc(env(safe-area-inset-bottom) * var(--nutui-safe-area-multiple, 1) );
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-sizing: border-box;
    box-shadow: 0px 0px 8px 0px #0000000a;
    z-index: 1200;

    .btn {
      flex: 1;
      height: 40px;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 14px;
      border-radius: 4px;
    }

    .cancel {
      margin-right: 12px;
      border: 1px solid #bebebe;
      color: #111111;
      background-color: #fff;
    }
    .sure {
      color: #fff;
      background-color: #f39800;
    }
  }

  .nut-dialog-outer {
    border-radius: 4px;
    width: calc(100% - 40px);

    .nut-dialog {
      padding: 24px 16px;
      width: 100%;
      .nut-dialog-header {
        font-size: 16px;
        line-height: 24px;
      }
      .nut-dialog-content {
        font-size: 14px;
        line-height: 20px;
        color: #707070;
        margin: 20px 0 24px;
        .nut-textarea {
          padding: 0;
          .nut-textarea-textarea {
            padding: 10px;
            height: 90px;
            border: 1px solid #f0f0f0;
            --nutui-textarea-text-color: #111111;
            line-height: 20px;
          }
        }
      }
      .nut-dialog-footer {
        .nut-button {
          width: 140px;
          height: 40px;
          padding: 0;
          box-sizing: border-box;
          border-radius: 4px;
          font-size: 13px;
          &:first-child {
            margin-right: 15px;
          }
        }
        .nut-dialog-footer-cancel {
          border: 1px solid #bebebe;
        }
        .nut-button-primary {
          background: #f39800 !important;
        }
      }
    }
  }
}
