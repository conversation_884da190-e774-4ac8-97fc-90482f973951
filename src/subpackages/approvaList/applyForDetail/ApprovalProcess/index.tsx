import Taro, { usePageScroll } from '@tarojs/taro'
import { Button, Text, View, ScrollView } from '@tarojs/components'
import { Tabs } from '@nutui/nutui-react-taro'
import { observer } from 'mobx-react'
import { useRef, useState, useEffect, useMemo } from 'react'
import { useSlientAuthDidShow } from '@/hook'
import IconFont from '@/components/iconfont'
import TabsUI from '@/subpackages/approvaList/components/Tabs'
import http from '@/http'
import './index.scss'
import classNames from 'classnames'

interface IndexType {
  info: any[]
}

const statusIcon = {
  开始: 'tijiao',
  同意: 'yitongyi',
  已通过: 'yitongyi',
  拒绝: 'jujue',
  待审批: 'daishenpi',
  已完成: 'jieshu',
  已审批: 'yitongyi',

}

const Index: React.FC<IndexType> = ({ info }) => {
  const getInitial = (name) => {
    const start = name.split(' ')
    return start[0][0]
  }

  const getColor = (item) => {
    if (item.spjg == '拒绝') {
      return '#E34D59'
    } else if (item.sfysp == '待审批') {
      return '#FF922F'
    } else {
      return '#66D23D'
    }
  }

  

  return (
    <View className='approval-process' id='approvalProcess'>
      <View className='title-bar'>
        <View className='title-left'>审批流程</View>
      </View>
      <View className='process-container'>
        <View className='process-list'>
          {info?.map((item, index) => {
            return (
              <View
                className={['process-item', index == info.length - 1 ? 'end last' : ''].join(' ')}
                key={index}
              >
                <View className='process-icon'>
                  <View className='icon-box' style={{ backgroundColor: getColor(item) }}>
                    <IconFont
                      name={item.spjg == '拒绝' ? statusIcon['拒绝'] : statusIcon[item.sfysp]}
                      size={12}
                      color='#fff'
                      style={{
                        marginTop: '2rpx',
                      }}
                    ></IconFont>
                  </View>
                </View>

                {item.wjzs == 1 ? (
                  <>
                    <View className='process-content'>已完成</View>
                  </>
                ) : (
                  <>
                    <View className='process-content'>
                      <View className='content-first-floor'>
                        <View className='process-info'>
                          <View className='info-avatar'></View>
                          <View className='info-txt'>
                            <View className='info-name'>{item.user_name}</View>
                            <View className='info-identity'>{item.sfysp}</View>
                          </View>
                        </View>
                        <View className='process-status'>
                          <View
                            className={[
                              'status-txt',
                              item.spjg == '拒绝' ? 'reject-text' : '',
                            ].join(' ')}
                          >
                            {item.spjg}
                          </View>
                          <View className='operate-time'>{item.spsj}</View>
                        </View>
                      </View>
                      {(item.spbz || item.spr_czxq) && (
                        <View className='process-remark'>
                          {item.spbz ? item.spbz + ';' : ''}
                          {item.spr_czxq}
                        </View>
                      )}
                    </View>
                  </>
                )}
              </View>
            )
          })}

          {/* {info.length && info[info.length - 1]['sfysp'] == '已完成' && (
            <View className='process-item end last'>
              <View className='process-icon'>
                <View className='icon-box'>
                  <IconFont name='jieshu' size={8} color='#fff'></IconFont>
                </View>
              </View>
              <View className='process-content'>已完成</View>
            </View>
          )} */}

          {/* <View className='process-item'>
            <View className='process-icon'>
              <View className='icon-box'>
                <IconFont
                  name='tijiao'
                  size={10}
                  color='#fff'
                  style={{
                    marginLeft: '2rpx',
                  }}
                ></IconFont>
              </View>
            </View>
            <View className='process-content'>
              <View className='content-first-floor'>
                <View className='process-info'>
                  <View className='info-avatar'></View>
                  <View className='info-txt'>
                    <View className='info-name'>xiaomi</View>
                    <View className='info-identity'>开始</View>
                  </View>
                </View>
                <View className='process-status'>
                  <View className='status-txt'>提交</View>
                  <View className='operate-time'>2025-04-12</View>
                </View>
              </View>
            </View>
          </View>
          <View className='process-item'>
            <View className='process-icon'>
              <View
                className='icon-box'
                style={{
                  backgroundColor: '#FF922F',
                }}
              >
                <IconFont name='daishenpi' size={12} color='#fff'></IconFont>
              </View>
            </View>
            <View className='process-content'>
              <View className='content-first-floor'>
                <View className='process-info'>
                  <View className='info-avatar'></View>
                  <View className='info-txt'>
                    <View className='info-name'>xiaomi</View>
                    <View className='info-identity'>开始</View>
                  </View>
                </View>
                <View className='process-status'>
                  <View className='status-txt'>提交</View>
                  <View className='operate-time'>2025-04-12</View>
                </View>
              </View>
            </View>
          </View>
          <View className='process-item'>
            <View className='process-icon'>
              <View
                className='icon-box'
                style={{
                  backgroundColor: '#E34D59',
                }}
              >
                <IconFont name='jujue' size={20} color='#fff'></IconFont>
              </View>
            </View>
            <View className='process-content'>
              <View className='content-first-floor'>
                <View className='process-info'>
                  <View className='info-avatar'></View>
                  <View className='info-txt'>
                    <View className='info-name'>xiaomi</View>
                    <View className='info-identity'>开始</View>
                  </View>
                </View>
                <View className='process-status'>
                  <View className='status-txt'>提交</View>
                  <View className='operate-time'>2025-04-12</View>
                </View>
              </View>
            </View>
          </View>
          <View className='process-item'>
            <View className='process-icon'>
              <View className='icon-box'>
                <IconFont
                  name='yitongyi'
                  size={12}
                  color='#fff'
                  style={{
                    marginTop: '2rpx',
                  }}
                ></IconFont>
              </View>
            </View>
            <View className='process-content'>
              <View className='content-first-floor'>
                <View className='process-info'>
                  <View className='info-avatar'></View>
                  <View className='info-txt'>
                    <View className='info-name'>xiaomi</View>
                    <View className='info-identity'>开始</View>
                  </View>
                </View>
                <View className='process-status'>
                  <View className='status-txt'>提交</View>
                  <View className='operate-time'>2025-04-12</View>
                </View>
              </View>
            </View>
          </View>
          <View className='process-item'>
            <View className='process-icon'>
              <View className='icon-box'>
                <IconFont
                  name='yitongyi'
                  size={12}
                  color='#fff'
                  style={{
                    marginTop: '2rpx',
                  }}
                ></IconFont>
              </View>
            </View>
            <View className='process-content'>
              <View className='content-first-floor'>
                <View className='process-info'>
                  <View className='info-avatar'></View>
                  <View className='info-txt'>
                    <View className='info-name'>xiaomi</View>
                    <View className='info-identity'>开始</View>
                  </View>
                </View>
                <View className='process-status'>
                  <View className='status-txt'>提交</View>
                  <View className='operate-time'>2025-04-12</View>
                </View>
              </View>
              <View className='process-remark'>
                仓管将SKU：TS0EEFF0412002 审批数调整为5；TS0EEFF0412002审批数调整为6
              </View>
            </View>
          </View>
          <View className='process-item end last'>
            <View className='process-icon'>
              <View className='icon-box'>
                <IconFont name='jieshu' size={8} color='#fff'></IconFont>
              </View>
            </View>
            <View className='process-content'>已完成</View>
          </View> */}
        </View>
      </View>
    </View>
  )
}

export default observer(Index)
