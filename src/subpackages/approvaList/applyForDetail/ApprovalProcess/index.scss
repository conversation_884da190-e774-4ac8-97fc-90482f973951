.approval-process{
    margin-top: 8px;
    padding: 16px 24px;
    background-color: #fff;
    .title-bar{
        display: flex;
        align-items: center;
        justify-content: space-between;
        .title-left{
            display: flex;
            align-items: center;
            color: #000;
            font-family: Noto Sans SC;
            font-weight: 500;
            font-size: 14px;
            line-height: 20px;
            &::before{
                content: "";
                width: 4px;
                height: 12px;
                margin-right: 4px;
                background-color: #F39800
            }
        }
    }
    .process-container{
        margin-top: 23px;
    }
    .process-list{
        .process-item{
            display: flex;
            &:not(:first-child){
                margin-top: 20px;
            }
            &.last{
                .process-icon{
                    &::before{
                        display: none;
                    }
                }
            }
            &.end{
                .process-icon{
                    .icon-box{
                        top: 6px;
                    }
                }
                .process-content{
                    align-items: flex-start;
                    color: rgba(190, 190, 190, 1);
                    font-family: Noto Sans SC;
                    font-weight: 500;
                    font-size: 14px;
                    line-height: 20px;
                }
            }
            .process-icon{
                flex: 1;
                position: relative;
                &::before{
                    content: "";
                    position: absolute;
                    left: 12px;
                    top: 37px;
                    height: 100%;
                    border-left: 1px dashed rgba(190, 190, 190, 1);
                }
                .icon-box{
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    position: absolute;
                    top: 15px;
                    left: 0;
                    width: 24px;
                    height: 24px;
                    border: 2px solid #fff;
                    box-sizing: border-box;
                    background-color: rgba(102, 210, 61, 1);
                    border-radius: 50%;
                }
            }
            .process-content{
                display: flex;
                flex-direction: column;
                align-items: flex-end;
                width: 291px;
                padding: 8px;
                background-color: rgba(247, 247, 247, 1);
                border-radius: 4px;
                box-sizing: border-box;
                .content-first-floor{
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    width: 100%;
                    .process-info{
                        display: flex;
                        align-items: center;
                        .info-avatar{
                            width: 32px;
                            height: 32px;
                            margin-right: 8px;
                            // background-color: rgba(42, 103, 255, 1);
                            border-radius: 6px;
                            display: flex;
                            justify-content: center;
                            align-items: center;
                            color: #Fff;
                            background: url('https://bq-oms-prod.oss-cn-zhangjiakou.aliyuncs.com/mms/images/%E9%BB%98%E8%AE%A4%E5%A4%B4%E5%83%8F.png?OSSAccessKeyId=LTAI5t6xTosFuZ2TyQuE1EWQ&Expires=2064483157&Signature=XQ%2F80xw68TFcMAVC0Oqhpp58794%3D') no-repeat center center;
                            background-size: 100% 100%;


                            
                        }
                        .info-txt{
                            .info-name{
                                color: rgba(0, 0, 0, 1);
                                font-family: Noto Sans SC;
                                font-weight: 500;
                                font-size: 14px;
                                line-height: 20px;
                            }
                            .info-identity{
                                margin-top: 2px;
                                color: rgba(153, 153, 153, 1);
                                font-family: Noto Sans SC;
                                font-weight: 400;
                                font-size: 12px;
                                line-height: 16px;
                            }
                        }
                    }
                    .process-status{
                        text-align: right;
                        .status-txt{
                            color: rgba(102, 210, 61, 1);
                            font-family: Noto Sans SC;
                            font-weight: 500;
                            font-size: 14px;
                            line-height: 20px;
                        }
                        .operate-time{
                            margin-top: 2px;
                            color: rgba(153, 153, 153, 1);
                            font-family: Noto Sans SC;
                            font-weight: 400;
                            font-size: 12px;
                            line-height: 16px;
                        }
                        .reject-text {
                          color: #E34D59;

                        }

                    }
                }
                .process-remark{
                    width: 235px;
                    border-radius: 4px;
                    box-sizing: border-box;
                    margin-top: 8px;
                    padding-top: 12px;
                    padding-right: 8px;
                    padding-bottom: 12px;
                    padding-left: 8px;
                    background-color: #fff;
                    border: 1px solid rgba(240, 240, 240, 1);
                    color: rgba(17, 17, 17, 1);
                    font-family: Noto Sans SC;
                    font-weight: 400;
                    font-size: 12px;
                    line-height: 16px;
                    margin-left: 40px;
                }
            }
        }
    }
}
