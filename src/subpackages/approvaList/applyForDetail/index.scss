.apply-for-detail {
  background-color: rgba(247, 248, 250, 1);

  .content {
    // overflow-y: scroll;
    display: flex;
    flex-direction: column;
  }

  .switch-tabs {
    display: flex;
    justify-content: center;
    width: 100%;
    height: 40px;
    background-color: #fff;
    color: rgba(153, 153, 153, 1);
    font-weight: 400;
    font-size: 14px;
    text-align: center;
    line-height: 20px;
    z-index: 10000;
    position: fixed;
    left: 0;

    .tab-item {
      position: relative;
      flex: 1;
      padding-top: 12px;
      &::before {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        width: 0;
        height: 2px;
        background-color: #f39800;
        transform: translateX(-50%);
      }
      &.active {
        color: #f39800;
        font-weight: 500;
        &::before {
          width: 56px;
          transition: 0.2s;
        }
      }
    }

    // .nut-tabs-content-wrap {
    //   display: none;
    // }
  }
  .switch-content {
    width: 100%;
    box-sizing: border-box;
    margin-top: 56px;
    // padding-bottom: calc(env(safe-area-inset-bottom) * var(--nutui-safe-area-multiple, 1) + 50px);
    overflow-y: scroll;
    flex: 1;
    padding-bottom: 40px;
  }

  .endBtn {
    padding: 9px 16px 0;
    box-shadow: 0px 0px 12px 0px #00000014;
    .endBtn-btn {
      font-weight: 400;
      font-size: 14px;
      line-height: 20px;
      text-align: center;
      height: 40px;
      border-radius: 4px;
      background-color: #F39800;
      color: #fff;
      display: flex;
      justify-content: center;
      align-items: center;
    
    }
  }
}
