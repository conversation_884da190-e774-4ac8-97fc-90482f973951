import Taro, { useRouter } from '@tarojs/taro'
import { View, ScrollView } from '@tarojs/components'
import { observer } from 'mobx-react'
import { useState, useEffect, useMemo } from 'react'
import { useStores } from '@/hook'
import http from '@/http'
import BaseInfo from '@/subpackages/approvaList/applyForDetail/BaseInfo'
import GoodsDetail from '@/subpackages/approvaList/applyForDetail/GoodsDetail'
import ApprovalProcess from '@/subpackages/approvaList/applyForDetail/ApprovalProcess'
import ExpressProgress from '@/subpackages/approvaList/applyForDetail/ExpressProgress'
import MainLayout from '@/components/MainLayout'
import { getNavBarInfo } from '@/utils'
import AnchorPoint from '@/components/anchorPoint'

import './index.scss'

const { bottomBarHeight } = getNavBarInfo()

const baseAnchors = [
  {
    label: '基本信息',
    value: 'baseInfo',
    path: '#baseInfo',
  },
  {
    label: '物料明细',
    value: 'goodsDetail',
    path: '#goodsDetail',
  },
]
const anchors = [
  {
    label: '基本信息',
    value: 'baseInfo',
    path: '#baseInfo',
  },
  {
    label: '物料明细',
    value: 'goodsDetail',
    path: '#goodsDetail',
  },
  {
    label: '审批流程',
    value: 'approvalProcess',
    path: '#approvalProcess',
  },
  {
    label: '物流进度',
    value: 'expressProgress',
    path: '#expressProgress',
  },
]

const Index = () => {
  const { navBarAndStatusBarHeight: barHeight,navBarAndStatusBarHeight } = getNavBarInfo()

  const [tabs, setTabs] = useState(anchors)
  const [curTab, setCurTab] = useState('#baseInfo')

  const { materialApplyStore } = useStores()
  const {baseInfo} = materialApplyStore
  const router = useRouter()

  const [routeParams] = useState(router.params)
  const [approveProcess, setApproveProcess] = useState([])

  const [domsInfo, setDomsInfo] = useState([])

  // useEffect(() => {
  //   getHeights()
  // }, [])

  // const getHeights = () => {
  //   const query = Taro.createSelectorQuery()
  //   for (let item of tabs) {
  //     query.select(`#${item.value}`).boundingClientRect()
  //   }
  //   query.exec((rect) => {
  //     setDomsInfo(rect)
  //   })
  // }

  useEffect(() => {
    if (routeParams.type == 2) {
      routeParams.is_xn == 0
        ? setTabs([
            ...baseAnchors,
            {
              label: '物流进度',
              value: 'expressProgress',
              path: '#expressProgress',
            },
          ])
        : setTabs([
            ...baseAnchors,
            {
              label: '审批流程',
              value: 'approvalProcess',
              path: '#approvalProcess',
            },
          ])
    } else {
      console.log('----anchors-', anchors)

      setTabs(anchors)
    }

    getHeadInfo()
    getGoodsInfo()
  }, [routeParams.id])

  const getHeadInfo = async () => {
    const res = await http.common.default.post(
      {
        dj_id: routeParams.id,
      },
      {},
      '?c=zdwld&m=base_info'
    )
    if (res.code == 0) {
      materialApplyStore.setBaseInfo(res.data)
      const { djlx, is_xn } = res.data

      if (djlx == 2) {
        is_xn == 1 && getApprovalProcess()
      } else {
        getApprovalProcess()
      }
    }
  }

  const getGoodsInfo = async () => {
    const res = await http.common.default.post(
      {
        dj_id: routeParams.id,
      },
      {},
      '?c=zdwld&m=wlsp_list'
    )
    if (res.code == 0) {
      materialApplyStore.setGoodList(res.data)
    }
  }

  const getApprovalProcess = async () => {
    const res = await http.common.default.post(
      {
        dj_id: routeParams.id,
      },
      {},
      '?c=zdwld&m=sp_lc'
    )
    if (res.code == 0) {
      setApproveProcess(res.data.list)
    }
  }

   const onConfirmEnd = async () => {
    const res = await http.common.default.post(
      {
        dj_id: baseInfo.dj_id,
      },
      {},
      '?c=zdwld&m=zz'
    )
    if (res.code == 0) {
      Taro.showToast({
        icon: 'none',
        title: '终止成功',
        success: () => {
          setTimeout(() => {
            Taro.switchTab({ url: '/pages/home/<USER>' })
          }, 1000)
        },
      })
    } else {
      Taro.showToast({
        title: res.msg,
        icon: 'error',
        duration: 2000,

      })
    }
  }

  return (
    <MainLayout
      className='apply-for-detail'
      initOptions={{
        inited: true,
        initLoading: true,
      }}
      headBarConfig={{
        showBack: true,
        showSearch: false,
        backgroundColor: '#fff',
        color: '#000',
        headerTitle:  ['0', '1'].includes(routeParams.type)?'物料申请明细':'物料归还明细',
        icon: '',
      }}
      style={{ backgroundColor: '#F7F8FA' }}
      showHeaderBar
      showTabBar={false}
    >
      <View className='content' style={{height:`calc(100vh - ${navBarAndStatusBarHeight}px)`}} >
        <View className='switch-tabs' id='switch-tabs' style={{ top: `${barHeight}px` }}>
          <AnchorPoint list={tabs} curTab={curTab} setCurTab={setCurTab} />
        </View>

        <View
          className='switch-content'
        >
          <BaseInfo></BaseInfo>
          <GoodsDetail></GoodsDetail>

          {['0', '1'].includes(routeParams.type) && (
            <>
              <ApprovalProcess info={approveProcess}></ApprovalProcess>
              <ExpressProgress></ExpressProgress>
            </>
          )}

          {routeParams.type == '2' && routeParams.is_xn == '0' && (
            <ExpressProgress></ExpressProgress>
          )}

          {routeParams.type == '2' && routeParams.is_xn == '1' && (
            <ApprovalProcess info={approveProcess}></ApprovalProcess>
          )}
        </View>

        <View className='endBtn' style={{paddingBottom:bottomBarHeight+'px',display: baseInfo.is_zz?'block':'none'}}>
          <View className='endBtn-btn' onClick={onConfirmEnd}>终止</View>
        </View>
      </View>
    </MainLayout>
  )
}

export default observer(Index)
