import Taro from '@tarojs/taro'
import { View, Image, Input } from '@tarojs/components'
import { ArrowDown, ArrowUp } from '@nutui/icons-react-taro'
import { observer } from 'mobx-react'
import { useRef, useState, useEffect } from 'react'
import { useStores } from '@/hook'
import IconFont from '@/components/iconfont'

// import { Input } from '@nutui/nutui-react-taro'

import _ from 'lodash'
import { statusText } from '@/utils/type'

import './index.scss'

const Index = () => {
  const [expand, setExpand] = useState(false)

  const [count, setCount] = useState(10)
  const { materialApplyStore } = useStores()
  const { goodList, baseInfo } = materialApplyStore
  const [goodWrapHeight, setGoodWrapHeight] = useState('auto')
  const [edit, setEdit] = useState(false)

  const [status, setStatus] = useState()

  useEffect(() => {
    if (baseInfo.djlx == 2) {
      setStatus('')
    } else {
      const key = Object.keys(statusText).find((k) => statusText[k] === baseInfo.zt)
      setStatus(key)
    }
  }, [baseInfo.zt])

  const onClickEdit = () => {
    setEdit(true)
  }

  const onUpdateSl = (info, number) => {
    const inventory = [0, 1, '0', '1'].includes(baseInfo.djlx) ? info.kysl : info.dgh_sl
    if (number > inventory || number < 0) {
      Taro.showToast({
        icon: 'none',
        title: number > inventory ? '审批数不能大于库存数' : '审批数不能小于0',
        duration: 2000,

      })
      materialApplyStore.setGoodList([...goodList])
    } else {
      const list = _.cloneDeep(goodList)
      const findItem = list.find((item) => item.barcode == info.barcode)
      findItem.sl_1 = number
      materialApplyStore.setGoodList(list)
    }
  }

  useEffect(() => {
    if (goodList.length > 3) {
      setGoodWrapHeight(Taro.pxTransform(130 * 3))
    } else {
      setGoodWrapHeight('auto')
    }
  }, [goodList])

  return (
    <View id='goodsDetail' className='goods-detail'>
      <View className='title-bar'>
        <View className='title-left'>物料明细</View>
        <View
          className='title-right'
          onClick={onClickEdit}
          style={{ display: status == 'awaitApprove' ? 'flex' : 'none' }}
        >
          <IconFont name='a-bianji11' size={10} color='#F39800' />
          编辑
        </View>
      </View>
      <View className='goods-wrap' style={{ height: goodWrapHeight }}>
        <View className='goods-list' id='goods-list'>
          {goodList?.map((item, index) => {
            return (
              <View className='goods-item' key={index}>
                <View className='goods-pic'>
                  <Image
                    src={item.img_url}
                    mode='aspectFill'
                    style={{
                      width: '100%',
                      height: '100%',
                    }}
                  ></Image>
                </View>
                <View className='goods-table'>
                  <View className='table-tr'>
                    <View className='table-th'>
                      <View className='table-cell'>名称</View>
                    </View>
                    <View className='table-td'>
                      <View className='table-cell '>
                        <View className='singleLineOmit'>{item.goods_name}</View>
                      </View>
                    </View>
                  </View>
                  <View className='table-row'>
                    <View className='table-col'>
                      <View className='table-tr'>
                        <View className='table-th'>
                          <View className='table-cell'>规格</View>
                        </View>
                        <View className='table-td'>
                          <View className='table-cell'>
                            <View className='singleLineOmit'>
                              {item.color_name}/{item.size_name}
                            </View>
                          </View>
                        </View>
                      </View>
                    </View>
                    <View className='table-col'>
                      <View className='table-tr'>
                        <View className='table-th'>
                          <View className='table-cell'>
                            {[0, 1, '0', '1'].includes(baseInfo.djlx) ? '申请数' : '归还数'}
                          </View>
                        </View>
                        <View className='table-td'>
                          <View className='table-cell'>{item.sl}</View>
                        </View>
                      </View>
                    </View>
                  </View>

                  {baseInfo.djlx == 2 ? (
                    <>
                      <View className='table-row'>
                        <View className='table-col'>
                          <View className='table-tr'>
                            <View className='table-th'>
                              <View className='table-cell'>参考价</View>
                            </View>
                            <View className='table-td'>
                              <View className='table-cell myInput'>{item.cke}</View>
                            </View>
                          </View>
                        </View>
                        <View className='table-col'>
                          <View className='table-tr'>
                            <View className='table-th'>
                              <View className='table-cell'>金额</View>
                            </View>
                            <View className='table-td'>
                              <View className='table-cell'>{item.je}</View>
                            </View>
                          </View>
                        </View>
                      </View>
                    </>
                  ) : (
                    <View className='table-row'>
                      <View className='table-col'>
                        <View className='table-tr'>
                          <View className='table-th'>
                            <View className='table-cell'>审批数</View>
                          </View>
                          <View className='table-td'>
                            <View className='table-cell myInput'>
                              {status == 'awaitApprove' && edit ? (
                                <Input
                                  focus={index == 0}
                                  type='number'
                                  placeholder=''
                                  value={item.sl_1}
                                  cursor={item?.sl_1?.length}
                                  onBlur={(v) => onUpdateSl(item, v.detail.value)}
                                />
                              ) : (
                                item.sl_1
                              )}
                            </View>
                          </View>
                        </View>
                      </View>
                      <View className='table-col'>
                        <View className='table-tr'>
                          <View className='table-th'>
                            <View className='table-cell'>仓库数</View>
                          </View>
                          <View className='table-td'>
                            <View className='table-cell'>{item.kysl}</View>
                          </View>
                        </View>
                      </View>
                    </View>
                  )}
                </View>
              </View>
            )
          })}
        </View>
      </View>

      {goodWrapHeight !== 'auto' && (
        <View
          className='look-more'
          onClick={() => {
            if (!expand) {
              setGoodWrapHeight(Taro.pxTransform(130 * goodList.length))
            } else {
              setGoodWrapHeight(Taro.pxTransform(130 * 3))
            }
            setExpand(!expand)
          }}
        >
          {expand ? '收起' : '查看更多'}
          <View className='down-arrow'>
            {expand ? (
              <ArrowUp size='24rpx' color='#000000' />
            ) : (
              <ArrowDown size='24rpx' color='#000000' />
            )}
          </View>
        </View>
      )}
    </View>
  )
}

export default observer(Index)
