.goods-detail {
  margin-top: 8px;
  padding: 12px 0 0;
  background-color: #fff;

  .title-bar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 24px;

    .title-left {
      display: flex;
      align-items: center;
      color: #000;
      font-family: Noto Sans SC;
      font-weight: 500;
      font-size: 14px;
      line-height: 20px;

      &::before {
        content: '';
        width: 4px;
        height: 12px;
        margin-right: 4px;
        background-color: #f39800;
      }
    }

    .title-right {
      display: flex;
      align-items: center;
      height: 24px;
      padding: 0 4px;
      color: #f39800;
      font-family: Noto Sans SC;
      font-weight: 400;
      font-size: 12px;
      line-height: 16px;

      .nut-icon-Add {
        margin-right: 2px;
      }
    }
  }

  .goods-list {
    .goods-item {
      display: flex;
      align-items: center;
      padding: 16px 24px;

      &:not(:first-child) {
        border-top: 0.5px solid #f0f0f0;
      }

      .goods-pic {
        width: 96px;
        height: 96px;
        margin-right: 12px;
      }

      .myInput {
        width: 50px !important;
        padding: 0 !important;

        .nut-input {
          padding: 0 4px 0 0 !important;

          .h5-input {
            font-size: 12px;
            color: #111;
          }
        }
      }
    }
  }

  .goods-wrap {
    overflow: hidden;
    height: 352px;
    transition: 0.3s;
  }

  .look-more {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 48px;
    color: #000000;
    font-family: Noto Sans SC;
    font-weight: 500;
    font-size: 12px;
    line-height: 16px;

    .down-arrow {
      display: flex;
      width: 12px;
      height: 12px;
      margin-left: 4px;
      transition: 0.3s;
    }
  }

  .goods-table {
    flex: 1;
    flex-shrink: 1;
    /* 允许子元素缩小 */
    min-width: 0;

    .table-tr {
      display: flex;
      height: 32px;

      .table-th {
        display: flex;
        align-items: center;
        flex-shrink: 0;
        width: 44px;
        background-color: rgba(247, 248, 250, 1);
        border: 0.5px solid rgba(240, 240, 240, 1);
        border-right: none;

        .table-cell {
          padding: 0 4px !important;
          display: flex;
          align-items: center;
          height: 100%;

          color: rgba(112, 112, 112, 1);
          font-family: Noto Sans SC;
          font-weight: 400;
          font-size: 12px;
          line-height: 16px;
          box-sizing: border-box;
        }
      }

      .table-td {
        flex-shrink: 1;
        /* 允许子元素缩小 */
        min-width: 0;
        display: flex;
        align-items: center;
        flex: 1;

        /* 确保内容可以溢出 */
        border: 0.5px solid rgba(240, 240, 240, 1);

        .table-cell {
          display: flex;
          align-items: center;
          flex-wrap: wrap;
          width: 100%;
          box-sizing: border-box;
          // padding: 8px 12px;
          padding: 8px 4px !important;

          color: rgba(17, 17, 17, 1);
          font-family: Noto Sans SC;
          font-weight: 400;
          font-size: 12px;
          line-height: 16px;
          word-break: break-all;
        }
      }
    }

    .table-row {
      display: flex;

      .table-col {
        width: 50%;
        .table-th {
          border-top: none;
        }
        .table-td {
          border-top: none;
        }
      }
      .table-col:first-child {
        .table-td {
          border-right: none;
        }
      }
    }

    .status-badge {
      height: 19px;
      margin-left: 8px;
      border-radius: 4px;
      padding-right: 8px;
      padding-left: 8px;
      background-color: rgba(16, 16, 16, 0.1);
      font-family: Noto Sans SC;
      font-weight: 500;
      color: rgba(16, 16, 16, 1);
      font-size: 10px;
      line-height: 19px;
    }
  }

  .operate-goods-list {
    .operate-goods-item {
      display: flex;
      align-items: center;
      padding: 12px 24px;

      &:not(:first-child) {
        border-top: 0.5px solid #f0f0f0;
      }

      .goods-pic {
        width: 104px;
        height: 104px;
      }

      .goods-info-operate {
        flex: 1;
        margin-left: 12px;

        .operate-top {
          display: flex;
          justify-content: space-between;

          .goods-left-info {
            display: flex;
            flex-direction: column;
            align-items: flex-start;

            .sku {
              color: #000000;
              font-family: Noto Sans CJK SC;
              font-weight: 700;
              font-size: 12px;
              line-height: 16px;
            }

            .goods-size {
              display: flex;
              align-items: center;
              height: 28px;
              margin-top: 4px;
              padding: 0 10px;
              background-color: #f6f6f6;
              color: #969696;
              font-family: Noto Sans CJK SC;
              font-weight: 400;
              font-size: 12px;
              line-height: 16px;
              border-radius: 4px;
            }
          }
        }

        .operate-btm {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-top: 16px;

          .goods-left-price {
            .unit-price {
              color: #e34d59;
              font-family: Matter;
              font-weight: 400;
              font-size: 12px;
              line-height: 18px;
              text-transform: capitalize;
            }

            .total-price {
              color: #000000;
              font-family: Noto Sans SC;
              font-weight: 500;
              font-size: 14px;
              line-height: 20px;
            }
          }

          .count-operate {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 98px;
            height: 32px;
            padding-right: 10px;
            padding-left: 10px;
            box-sizing: border-box;
            border-radius: 16px;
            border: 1px solid #dedede;
            --nutui-inputnumber-input-background-color: transparent;
            --nutui-inputnumber-input-font-color: #000;
            --nutui-inputnumber-input-font-size: 12px;
            --nutui-inputnumber-icon-size: 32px;

            .nut-inputnumber {
              width: 100%;
            }

            .nut-number-input {
              font-size: 12px;
            }

            .nut-input-minus,
            .nut-input-add {
              flex: 1;

              .nut-icon {
                transform: scale(1.4);
              }
            }
          }
        }
      }
    }
  }
}
