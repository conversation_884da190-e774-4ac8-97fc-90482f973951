import Taro from '@tarojs/taro'
import { Icon, View } from '@tarojs/components'
import { TextArea, DatePicker, type PickerOption } from '@nutui/nutui-react-taro'
import { observer } from 'mobx-react'
import { useState, useEffect } from 'react'
import { useStores } from '@/hook'
import IconFont from '@/components/iconfont'

import { statusText, orderStatus } from '@/utils/type'
import LogisticsPopup from '../../components/LogisticsPopup'
import './index.scss'

interface IndexType {
  // statusWord: string
}

export interface InfoType {
  djlx: string | number
  zdrq: string
  djbh: string
  zdr: string
  sl: string
  zdmc: string
  gkname: string
  phone: string
  address: string
  bz: string
  zt: string
  province: string
  city: string
  district: string
  sl_1: string
  ghrq: string
  zddm: string
  shipping_sn: string
}

const Index: React.FC<IndexType> = () => {
  const { materialApplyStore } = useStores()
  const info: InfoType = materialApplyStore.baseInfo
  const [show, setShow] = useState(false)
  const [returnDate, setReturnDate] = useState<string>()
  const [selectDate, setSelectDate] = useState<Date>()
  const [status, setStatus] = useState()
  const [diffNumber, setDiffNumber] = useState(0)
  const [orderType, setOrderType] = useState('')
  const [logisticsPopupVisible, setLogisticsPopupVisible] = useState(false)

  const formatDate = (dateStr: string) => {
    const [date, time] = dateStr.split(' ')
    const [year, month, day] = date.split('-').map(Number)
    const [hour, minute] = time.split(':').map(Number)
    return new Date(year, month - 1, day, hour, minute, 0)
  }

  useEffect(() => {
    const key = Object.keys(statusText).find((k) => statusText[k] === info.zt)
    setStatus(key)
  }, [info.zt])

  useEffect(() => {

    console.log('----info',info);
    
    // 确定归还时间
    if (info.djlx == 1) {
      if (info.ghrq) {
        setReturnDate(info.ghrq)
      }
    }

    // 确定审批差异数
    if (info.sl) {
      const number = Number(info.sl_1 ? info.sl_1 : 0)
      setDiffNumber(Number(info.sl) - number)
    }

    // 确定单据类型
    if (info.djlx == 2) {
      setOrderType(info.is_xn == 1 ? '异常核销归还' : '实物归还')
    } else {
      setOrderType(orderStatus[info.djlx])
    }
  }, [info])

  const onNavigateTo = () => {
    Taro.navigateTo({
      url: `/subpackages/approvaList/modifyAddress/index?type=${info.djlx}`,
    })
  }
  const change = (options: PickerOption[], values: (string | number)[]) => {
    const date = values.slice(0, 3).join('-')
    const time = values.slice(3).join(':')
    const dateStr = `${date} ${time}`
    setReturnDate(dateStr)
    setSelectDate(formatDate(dateStr))
    materialApplyStore.setBaseInfo({ ...info, ghrq: dateStr })
  }

  const onRemarkChange = (v, p) => {
    const tempInfo = { ...info, [p]: v }
    materialApplyStore.setBaseInfo(tempInfo)
  }

  return (
    <View id='baseInfo' className='base-info'>
      <View className='title-bar'>
        <View className='title-left'>基本信息</View>
      </View>
      <View className='table-info'>
        <View className='table-tr'>
          <View className='table-th'>
            <View className='table-cell'>单据编号</View>
          </View>
          <View className='table-td'>
            <View className='table-cell'>
              {info.djbh}
              <View className={['status-badge', status].join(' ')}>{info.zt}</View>
            </View>
          </View>
        </View>
        <View className='table-tr'>
          <View className='table-th'>
            <View className='table-cell'>创建时间</View>
          </View>
          <View className='table-td'>
            <View className='table-cell'>{info.zdrq}</View>
          </View>
        </View>
        {info.djlx == 1 && (
          <View className='table-tr'>
            <View className='table-th'>
              <View className='table-cell'>归还时间</View>
            </View>
            <View className='table-td'>
              <View
                className='table-cell'
                style={{
                  justifyContent: 'space-between',
                }}
              >
                {returnDate}
                <View
                  className='editIcon'
                  onClick={() => setShow(true)}
                  style={{
                    display: info.zt == '待提交' ? 'flex' : 'none',
                  }}
                >
                  <IconFont name='a-bianji11' size={16} color='#F39800'></IconFont>
                </View>
              </View>
            </View>
          </View>
        )}

        <View className='table-row'>
          <View className='table-col'>
            <View className='table-tr'>
              <View className='table-th'>
                <View className='table-cell'>单据类型</View>
              </View>
              <View className='table-td'>
                <View className='table-cell'>{orderType}</View>
              </View>
            </View>
          </View>
          <View className='table-col'>
            <View className='table-tr'>
              <View className='table-th'>
                <View className='table-cell'>{info.djlx == 2 ? '归还' : '申请'}数</View>
              </View>
              <View className='table-td'>
                <View className='table-cell'>{info.sl}</View>
              </View>
            </View>
          </View>
        </View>

        {['已完成', '已审批'].includes(info.zt) && (
          <View className='table-row'>
            <View className='table-col'>
              <View className='table-tr'>
                <View className='table-th'>
                  <View className='table-cell'>审批数</View>
                </View>
                <View className='table-td'>
                  <View className='table-cell'>{info.sl_1}</View>
                </View>
              </View>
            </View>
            <View className='table-col'>
              <View className='table-tr'>
                <View className='table-th'>
                  <View className='table-cell'>差异数</View>
                </View>
                <View className='table-td'>
                  <View className='table-cell'>{diffNumber}</View>
                </View>
              </View>
            </View>
          </View>
        )}

        <View className='table-row'>
          <View className='table-col'>
            <View className='table-tr'>
              <View className='table-th'>
                <View className='table-cell'>制单人</View>
              </View>
              <View className='table-td'>
                <View className='table-cell'>{info.zdr}</View>
              </View>
            </View>
          </View>
          <View className='table-col'>
            <View className='table-tr'>
              <View className='table-th'>
                <View className='table-cell'>门店代码</View>
              </View>
              <View className='table-td'>
                <View className='table-cell'>{info.zddm}</View>
              </View>
            </View>
          </View>
        </View>

        {!orderType?.includes('异常核销归还') && (
          <View className='table-tr'>
            <View className='table-th'>
              <View className='table-cell'>
                {orderType == '实物归还' ? '仓库信息' : '收货信息'}
              </View>
            </View>
            <View className='table-td'>
              <View className='table-cell'>
                <View
                  style={{
                    width: '100%',
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                  }}
                >
                  {info.gkname} {info.phone}
                  {info.zt == '待提交' && !orderType?.includes('归还')  && (
                    <View className='editIcon' onClick={onNavigateTo}>
                      <IconFont name='a-bianji11' size={16} color='#F39800'></IconFont>
                    </View>
                  )}
                </View>
                <View
                  style={{
                    color: '#707070',
                    marginTop: '8rpx',
                  }}
                >
                  {info.province + info.city + info.district + info.address}
                </View>
              </View>
            </View>
          </View>
        )}

        {orderType == '实物归还' && (
          <View className='table-tr'>
            <View className='table-th shipping'>
              <View className='table-cell shipping'>
                物流单号
                <View className='star'>*</View>
              </View>
            </View>
            <View className='table-td'>
              <View
                className='table-cell myTextArea'
                style={{
                  justifyContent: 'space-between',
                }}
              >
                <View className='shippingArea'>
                  {info.shipping_sn ? (
                    <View className='shippingContent'>
                      <View  className='shippingName'>{info.shipping_name}</View>
                      <View className='shippingSn'>{info.shipping_sn}</View>
                    </View>
                  ) : (
                    '请选择物流公司'
                  )}
                </View>

                {['待提交'].includes(info.zt) && (
                  <View className='editIcon' onClick={() => setLogisticsPopupVisible(true)}>
                    <IconFont name='icon' size={20} />
                  </View>
                )}
              </View>
            </View>
          </View>
        )}

        <View className='table-tr'>
          <View className='table-th'>
            <View className='table-cell'>备注</View>
          </View>
          <View className='table-td'>
            <View
              className='table-cell myTextArea'
              style={{
                justifyContent: 'space-between',
              }}
            >
              {['待提交'].includes(info.zt) ? (
                <TextArea
                  value={info.bz}
                  rows={1}
                  autoSize
                  onChange={(v) => onRemarkChange(v, 'bz')}
                />
              ) : (
                <>{info.bz ? info.bz : '无'}</>
              )}
            </View>
          </View>
        </View>
      </View>

      <DatePicker
        className='materialDatePicker'
        title='日期选择'
        visible={show}
        startDate={info.zdrq ? formatDate(info.zdrq) : new Date()}
        value={selectDate}
        showChinese
        onClose={() => setShow(false)}
        threeDimensional={false}
        type='datetime'
        onConfirm={(options, values) => change(options, values)}
      />

      <LogisticsPopup visible={logisticsPopupVisible} setVisible={setLogisticsPopupVisible} />
    </View>
  )
}

export default observer(Index)
