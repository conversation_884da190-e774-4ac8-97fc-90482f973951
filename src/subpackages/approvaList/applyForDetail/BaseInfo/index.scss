.base-info {
  padding: 12px 24px;
  background-color: #fff;

  .title-bar {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .title-left {
      display: flex;
      align-items: center;
      color: #000;
      font-family: Noto Sans SC;
      font-weight: 500;
      font-size: 14px;
      line-height: 20px;

      &::before {
        content: '';
        width: 4px;
        height: 12px;
        margin-right: 4px;
        background-color: #f39800;
      }
    }
  }

  .table-info {
    margin-top: 16px;

    .table-tr {
      display: flex;
      height: 100%;

      .table-th {
        display: flex;
        align-items: center;
        flex-shrink: 0;
        width: 64px;
        background-color: rgba(247, 248, 250, 1);
        border: 0.5px solid rgba(240, 240, 240, 1);
        border-right: none;

        .table-cell {
          padding: 12px 8px;
          color: rgba(112, 112, 112, 1);
          font-family: Noto Sans SC;
          font-weight: 400;
          font-size: 12px;
          line-height: 16px;
        }
      }

      .table-td {
        display: flex;
        align-items: center;
        flex: 1;
        border: 0.5px solid rgba(240, 240, 240, 1);

        .table-cell {
          display: flex;
          align-items: center;
          flex-wrap: wrap;
          width: 100%;
          box-sizing: border-box;
          padding: 12px;
          color: rgba(17, 17, 17, 1);
          font-family: Noto Sans SC;
          font-weight: 400;
          font-size: 12px;
          line-height: 16px;
          word-break: break-all;

          .editIcon {
            width: 20px;
          }
        }

        .myTextArea {
          .nut-textarea {
            padding: 0;
            flex: 1;

            .nut-textarea-textarea {
              color: #999999;
              font-size: 12px;
              line-height: 16px;
              color: #111;
            }
          }

          .shippingArea {
            font-weight: 400;
            font-size: 12px;
            line-height: 16px;

            color: #999999;
            .shippingContent {
              .shippingName {
                color: #111111;
              }
              .shippingSn {
                color: #707070;
              }
              

            }


          }
        }
      }
    }

    .table-tr:not(:first-child) {
      .table-th {
        border-top: none;
      }
      .table-td {
        border-top: none;
      }
    }

    .table-row {
      display: flex;

      .table-col {
        width: 50%;
        .table-tr {
          .table-th,
          .table-td {
            border-top: none;
          }
        }
      }
      .table-col:first-child {
        .table-td {
          border-right: none;
        }
      }
    }

    .status-badge {
      height: 19px;
      margin-left: 8px;
      border-radius: 4px;
      padding-right: 8px;
      padding-left: 8px;
      background-color: rgba(16, 16, 16, 0.1);
      font-family: Noto Sans SC;
      font-weight: 500;
      color: rgba(16, 16, 16, 1);
      font-size: 10px;
      line-height: 19px;
    }

    .apply {
      background-color: rgba(42, 103, 255, 0.16);
      color: #2a67ff;
    }

    .going {
      background-color: rgba(253, 169, 17, 0.16);
      color: #fda911;
    }

    .view {
      background-color: rgba(95, 184, 135, 0.16);
      color: #5fb887;
    }
    .awaitApprove,
    .awaitStorage {
      background-color: rgba(253, 169, 17, 0.16);
      color: #fda911;
    }

    .view {
      background-color: rgba(17, 17, 17, 0.1);
      color: #111111;
    }
  }
  .materialDatePicker {
    .nut-picker-cancel-btn {
      font-size: 14px;
      color: #707070;
    }
    .nut-picker-title {
      font-size: 14px;
      font-weight: 400;
    }
    .nut-picker-confirm-btn {
      font-size: 14px;
      font-weight: 500;
      color: #f39800;
    }
    .nut-picker-roller-item-title {
      font-size: 14px;
    }
    .nut-popup {
      z-index: 10000 !important;
    }
  }
  .shipping {
    display: flex;
    align-items: center;
    position: relative;
    .star {
      position: absolute;
      font-size: 12px;
      right: 2px;
      top: 10px;
      color: #e34d59;
    }
  }
}
