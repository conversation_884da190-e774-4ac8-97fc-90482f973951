.express-progress {
  margin-top: 8px;
  padding: 12px 24px 16px;
  background-color: #fff;

  .title-bar {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .title-left {
      display: flex;
      align-items: center;
      color: #000;
      font-family: Noto Sans SC;
      font-weight: 500;
      font-size: 14px;
      line-height: 20px;

      &::before {
        content: '';
        width: 4px;
        height: 12px;
        margin-right: 4px;
        background-color: #f39800;
      }
    }
  }

  .packageContent {
    margin-top: 16px;

    .nut-tabs-titles {
      height: 36px;

      .nut-tabs-titles-item {
        height: 100%;
        background-color: #fff;
        padding: 0 6px;

        .nut-tabs-titles-item-text {
          height: 100%;
          padding: 0 32px;
          color: #111111;
          border: 1px solid #bebebe;
          border-radius: 4px;
          font-size: 14px;
          background-color: #fff;
          box-sizing: border-box;
        }
      }

      .nut-tabs-titles-item:first-child {
        padding-left: 0;
      }

      .nut-tabs-titles-item:last-child {
        padding-right: 0;
      }

      .nut-tabs-titles-item-active {
        .nut-tabs-titles-item-text {
          border: 1px solid #f39800;
          color: #f39800;
        }
      }
    }
  }

  .express-info {
    margin-top: 16px;

    .express-order {
      display: flex;
      align-items: center;
      padding: 6px 8px;
      background-color: #f7f7f7;
      font-family: Noto Sans SC;
      font-weight: 400;
      font-size: 14px;
      line-height: 20px;
      border-radius: 4px;

      .express-name {
        color: #111111;
      }

      .order-number {
        color: #707070;
        margin-left: 10px;
        margin-right: 4px;
      }
    }
  }

  .progress-wrap {
    margin-top: 8px;
    background-color: #f7f7f7;
    border-radius: 4px;

    .progress-container {
    }

    .progress-list {
      padding: 8px 15px 24px 18px;

      .progress-item {
        display: flex;

        &:not(:last-child) {
          .progress-right {
            padding-bottom: 16px;
          }
        }

        &:last-child {
          .progress-left {
            .progress-line {
              display: none;
            }
          }
        }

        .progress-left {
          flex: 1;
          position: relative;

          .progress-line {
            position: absolute;
            top: 8px;
            left: 0;
            width: 0;
            height: 100%;
            border-left: 1px solid #bebebe;
          }

          .progress-dot {
            position: absolute;
            left: -7px;
            top: 3px;
            width: 8px;
            height: 8px;
            border: 4px solid #f7f7f7;
            background-color: #bebebe;
            border-radius: 50%;
          }

          .progress-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            position: absolute;
            left: -13px;
            top: -4px;
            width: 20px;
            height: 20px;
            border: 4px solid #f7f7f7;
            background-color: #f39800;
            border-radius: 50%;
          }
        }

        .progress-right {
          width: 279px;

          .progress-name {
            margin-bottom: 8px;
            color: #000;
            font-family: Noto Sans SC;
            font-weight: 500;
            font-size: 14px;
            line-height: 20px;
          }

          .progress-desc {
            color: #707070;
            font-family: Noto Sans SC;
            font-weight: 400;
            font-size: 12px;
            line-height: 16px;
          }

          .progress-time {
            margin-top: 4px;
            color: #999999;
            font-family: Noto Sans SC;
            font-weight: 400;
            font-size: 10px;
            line-height: 12px;
          }
        }
      }
    }

    .progress-btn-wrap {
      display: flex;
      justify-content: center;
      padding-bottom: 12px;
      margin-top: 20px;

      .progress-btn {
        display: flex;
        align-items: center;
        height: 26px;
        padding: 0 7.5px;
        border: 1px solid #bebebe;
        box-sizing: border-box;
        color: #000;
        font-family: Noto Sans SC;
        font-weight: 400;
        font-size: 12px;
        line-height: 16px;
        border-radius: 4px;
      }
    }
  }
}
.singleRow {
  padding-bottom: 20px;
}
