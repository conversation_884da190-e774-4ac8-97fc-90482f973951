import Taro, { usePageScroll } from '@tarojs/taro'
import { Button, Text, View, ScrollView } from '@tarojs/components'
import { Tabs } from '@nutui/nutui-react-taro'
import { observer } from 'mobx-react'
import { useRef, useState, useEffect, useMemo } from 'react'
import { useSlientAuthDidShow, useStores } from '@/hook'
import TabsUI from '@/subpackages/approvaList/components/Tabs'
import http from '@/http'
import IconFont from '@/components/iconfont'
import { InfoType } from '../BaseInfo'

import './index.scss'
import classNames from 'classnames'

const icons = {
  已揽收: 'yilanshou',
  运输中: 'yunshuzhong1',
  派送中: 'paisongzhong',
  已签收: 'yiqianshou',
  已退回: 'yituihui',
  问题件: 'wentijian',
  拒收: 'jushou',
  初始: 'chushi',
  退件签收: 'yiqianshou',
}

interface TransportInfo {
  AcceptStatus: string
  opcode: string
  AcceptStation: string
  AcceptTime: string
}

const Index = ({  }) => {
  const [tabValue, setTabValue] = useState(0)
  const { materialApplyStore } = useStores()
  const baseInfo: InfoType = materialApplyStore.baseInfo

  const [orders, setOrders] = useState(['DPK202037209220'])
  const [transportInfo, setTransportInfo] = useState<TransportInfo[]>([])
  const [wholeTransportInfo, setWholeTransportInfo] = useState<TransportInfo[]>([])

  const [expand, setExpand] = useState(false)
  const [result,setResult] = useState<TransportInfo[]>([])
  
  

  useEffect(() => {
    if (baseInfo.shipping_sn) {
      const res = baseInfo.shipping_sn.split(',')
      setOrders(res)
      setTabValue(res[0])
      getExpressProgress(res[0])
    } else {
      setTransportInfo([])
      setWholeTransportInfo([])
      setResult([])
    }
  }, [baseInfo.shipping_sn])


  useEffect(() => {

  },[result])

  const getExpressProgress = async (params) => {
    const res = await http.common.default.post(
      {
        LogisticCode: params,
        djbh:baseInfo.djbh
      },

      {},
      '?c=zdwld&m=shipping_log'
    )
    if (res.code == 0) {
      setTransportInfo([res.data[0]])
      setWholeTransportInfo(res.data)
      if(res.data.length == 0) {
        setTransportInfo([])
        setResult([])
      }else {
        setResult([res.data[0]])
        setTransportInfo([res.data[0]])

      }

      
    }
  }

  const onClipBoard = (order) => {
    Taro.setClipboardData({
      data: order,
      success: function (res) {
        Taro.getClipboardData({
          success: function (res) {
            console.log(res.data) // data
          },
        })
      },
    })
  }

  return (
    <View id='expressProgress' className='express-progress'>
      <View className='title-bar'>
        <View className='title-left'>物流进度</View>
      </View>

      <View>
        {baseInfo.shipping_sn ? (
          <>
            {orders.length > 1 && (
              <View className='packageContent'>
                <Tabs
                  value={tabValue}
                  onChange={(value) => {
                    setExpand(false)
                    setTabValue(value)
                    getExpressProgress(value)
                  }}
                  activeType='button'
                >
                  {orders.map((item, index) => {
                    return (
                      <Tabs.TabPane
                        title={`包裹${index + 1}`}
                        key={item}
                        value={item}
                      ></Tabs.TabPane>
                    )
                  })}
                </Tabs>
              </View>
            )}

            <View className='express-info'>
              <View className='express-order'>
                <Text className='express-name'>{baseInfo.shipping_name}</Text>
                <Text className='order-number'>{tabValue}</Text>
                <View onClick={() => onClipBoard(tabValue)}>
                  <IconFont name='content_copy' size={12} color='#707070'></IconFont>
                </View>
              </View>
              <View className='progress-wrap'>
                <View
                  className='progress-container'
                  style={{ height: 'auto' }}
                >
                  <View className='progress-list'>
                    <View className='progress-item'>
                      <View className='progress-left'>
                        <View className='progress-line'></View>
                        <View className='progress-dot'></View>
                      </View>
                      <View className='progress-right'>
                        <View className='progress-name'>
                          【收货地址】
                          {baseInfo.province + baseInfo.city + baseInfo.district + baseInfo.address}
                        </View>
                        <View className='progress-desc'>
                          {baseInfo.gkname} {baseInfo.phone}
                        </View>
                      </View>
                    </View>

                    {result?.map((item, index) => {
                      return (
                        <View className='progress-item' key={item?.opcode}>
                          <View className='progress-left'>
                            <View className='progress-line'></View>
                            <View
                              className='progress-icon'
                              style={{
                                background: index ? '#BEBEBE' : '#F39800',
                              }}
                            >
                              <IconFont
                                name={icons[item?.AcceptStatus]}
                                size={20}
                                color='#fff'
                              ></IconFont>
                            </View>
                          </View>
                          <View className='progress-right'>
                            <View
                              className='progress-name'
                              style={{
                                color: index ? '#BEBEBE' : '#F39800',
                              }}
                            >
                              {item?.AcceptStatus}
                            </View>
                            <View className='progress-desc'>{item?.AcceptStation}</View>
                            <View className='progress-time'>{item?.AcceptTime}</View>
                          </View>
                        </View>
                      )
                    })}
                  </View>
                </View>
                {(!expand && wholeTransportInfo.length>1 )  && (
                  <View className='progress-btn-wrap'>
                    <View className='progress-btn' onClick={() => {
                      setExpand(true)
                      setResult(wholeTransportInfo)
                    }}
                    >
                      完整物流进度
                      <IconFont name='icon' size={12} color='#000000'></IconFont>
                    </View>
                  </View>
                )}
              </View>
            </View>
          </>
        ) : (
          <View className='express-info  singleRow'>
            <View className='express-order'>
              <Text className='express-name'>
                {baseInfo?.zt == '已完成' ? '待发货' : '暂无数据'}
              </Text>
            </View>
          </View>
        )}
      </View>
    </View>
  )
}

export default observer(Index)
