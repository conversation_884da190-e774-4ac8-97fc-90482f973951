/* eslint-disable import/no-commonjs */
const inquirer = require('inquirer')
const shell = require('shelljs')

inquirer
  .prompt([
    {
      type: 'list',
      name: 'platform',
      message: '请选择平台:',
      choices: [
        'weapp',
        'swan',
        'alipay',
        'tt',
        'h5',
        'rn',
        'qq',
        'jd',
        'quickapp',
      ],
    },
    {
      type: 'list',
      name: 'apiEnv',
      message: '请选择后端接口环境:',
      choices: ['dev', 'production'],
    },
    {
      type: 'list',
      name: 'nodeEnv',
      message: '请选择编译环境:',
      choices: ['development', 'production'],
    },
  ])
  .then((answers) => {
    const cli = 'C:\\"Program Files (x86)"\\Tencent\\微信web开发者工具\\cli.bat'
    shell.exec(`${cli} open --project ${process.cwd()}`)
    const { platform, apiEnv, nodeEnv } = answers
    shell.exec(
      `npx cross-env OUTPUT=dist NODE_ENV=${nodeEnv} API_ENV=${apiEnv} taro build --type ${platform} --watch`,
    )
  })
  .catch((err) => {
    console.log('启动出错:', err)
  })
